# تثبيت PHPMailer - نظام التواصل الذكي

## الطريقة الأولى: استخدام Composer (الأفضل)

### 1. تثبيت Composer
إذا لم يكن Composer مثبتاً على جهازك:

**Windows:**
1. حمّل Composer من: https://getcomposer.org/download/
2. شغل ملف التثبيت واتبع التعليمات

**أو استخدم XAMPP:**
1. افتح Command Prompt كمدير
2. اذهب إلى مجلد المشروع:
```bash
cd C:\xampp\htdocs\smart-communication-
```

### 2. تثبيت PHPMailer
```bash
composer install
```

أو إذا لم يعمل الأمر أعلاه:
```bash
composer require phpmailer/phpmailer
```

## الطريقة الثانية: التحميل اليدوي

### 1. تحميل PHPMailer
1. اذهب إلى: https://github.com/PHPMailer/PHPMailer/releases
2. حمّل أحدث إصدار (PHPMailer-6.x.x.zip)
3. فك الضغط

### 2. نسخ الملفات
1. أنشئ مجلد `vendor/phpmailer/phpmailer/src` في مجلد المشروع
2. انسخ جميع ملفات `src` من PHPMailer إلى المجلد الجديد
3. أنشئ ملف `vendor/autoload.php` بالمحتوى التالي:

```php
<?php
// PHPMailer Autoloader
spl_autoload_register(function ($class) {
    $prefix = 'PHPMailer\\PHPMailer\\';
    $base_dir = __DIR__ . '/phpmailer/phpmailer/src/';
    
    $len = strlen($prefix);
    if (strncmp($prefix, $class, $len) !== 0) {
        return;
    }
    
    $relative_class = substr($class, $len);
    $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';
    
    if (file_exists($file)) {
        require $file;
    }
});
?>
```

## الطريقة الثالثة: استخدام النظام بدون PHPMailer

النظام يعمل حالياً بدون PHPMailer باستخدام `mail()` function المدمجة في PHP، لكن ستحتاج إلى:

### 1. إعداد SMTP في XAMPP
1. افتح ملف `C:\xampp\php\php.ini`
2. ابحث عن `[mail function]`
3. عدّل الإعدادات:

```ini
[mail function]
SMTP = smtp.gmail.com
smtp_port = 587
sendmail_from = <EMAIL>
sendmail_path = "\"C:\xampp\sendmail\sendmail.exe\" -t"
```

### 2. إعداد Sendmail
1. افتح ملف `C:\xampp\sendmail\sendmail.ini`
2. عدّل الإعدادات:

```ini
[sendmail]
smtp_server=smtp.gmail.com
smtp_port=587
error_logfile=error.log
debug_logfile=debug.log
auth_username=<EMAIL>
auth_password=your-app-password
force_sender=<EMAIL>
```

### 3. إعداد Gmail App Password
1. اذهب إلى إعدادات Google Account
2. فعّل 2-Step Verification
3. أنشئ App Password للتطبيق
4. استخدم App Password بدلاً من كلمة المرور العادية

## التحقق من التثبيت

### 1. اختبار النظام
1. اذهب إلى: `http://localhost/smart-communication-/test-system.php`
2. تحقق من حالة "إعدادات البريد الإلكتروني"

### 2. اختبار الإرسال
1. سجل دخول للنظام
2. اذهب إلى "إرسال بريد إلكتروني"
3. جرب إرسال رسالة تجريبية

## حل المشاكل الشائعة

### خطأ "vendor/autoload.php not found"
- تأكد من تشغيل `composer install`
- أو استخدم الطريقة الثانية للتحميل اليدوي

### خطأ "SMTP connect() failed"
- تحقق من إعدادات SMTP
- تأكد من صحة اسم المستخدم وكلمة المرور
- استخدم App Password لـ Gmail

### خطأ "mail() function failed"
- تحقق من إعدادات php.ini
- تأكد من إعداد sendmail.ini
- أعد تشغيل Apache

### خطأ "Authentication failed"
- تأكد من تفعيل 2-Step Verification في Gmail
- استخدم App Password بدلاً من كلمة المرور العادية
- تحقق من صحة البيانات

## إعدادات Gmail الموصى بها

```
SMTP Host: smtp.gmail.com
SMTP Port: 587
SMTP Security: TLS
Username: <EMAIL>
Password: your-app-password (ليس كلمة المرور العادية)
```

## إعدادات Outlook/Hotmail

```
SMTP Host: smtp-mail.outlook.com
SMTP Port: 587
SMTP Security: TLS
Username: <EMAIL>
Password: your-password
```

## ملاحظات مهمة

1. **الأمان**: لا تستخدم كلمة المرور العادية، استخدم App Passwords
2. **الأداء**: PHPMailer أفضل من mail() function
3. **التوافق**: النظام يعمل مع الطريقتين
4. **الاختبار**: اختبر الإعدادات قبل الاستخدام الفعلي

## الدعم الفني

إذا واجهت مشاكل في التثبيت:
- راجع ملف `logs/error.log`
- استخدم صفحة `test-system.php`
- تواصل مع الدعم الفني

---

© 2024 نظام التواصل الذكي
