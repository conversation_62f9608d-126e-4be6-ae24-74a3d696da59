# نظام التواصل الذكي - Smart Communication System

نظام تواصل ذكي ومتكامل يتيح للموظفين التواصل مع العملاء عبر البريد الإلكتروني وواتساب بطريقة احترافية.

## المميزات

### إدارة المستخدمين والموظفين
- نظام تسجيل دخول آمن مع صلاحيات متعددة (مدير، مشرف، موظف)
- ربط كل موظف بقائمة عملائه الخاصة
- سجل دخول وخروج للموظفين
- حماية الجلسات والبيانات

### إدارة العملاء
- قاعدة بيانات شاملة للعملاء مع جميع المعلومات المطلوبة
- تصنيف العملاء حسب القطاع (تعليمي، حكومي، خاص)
- إمكانية البحث والتصفية المتقدمة
- تصنيف العملاء حسب الأولوية والحالة

### نظام التواصل
- إرسال رسائل البريد الإلكتروني باستخدام PHPMailer
- إرسال رسائل واتساب عبر API أو روابط مباشرة
- قوالب رسائل جاهزة قابلة للتخصيص
- سجل كامل لجميع التواصلات

### لوحة التحكم والإحصائيات
- إحصائيات تفاعلية ومفصلة
- رسوم بيانية لحجم التواصل
- تقارير يومية وأسبوعية وشهرية

### نظام الإشعارات
- تنبيهات للموظفين عن العملاء غير المتابعين
- تذكيرات تلقائية للمتابعة
- إشعارات فورية للأحداث المهمة

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx مع mod_rewrite
- Composer لإدارة التبعيات
- XAMPP (للتطوير المحلي)

## التثبيت

### 1. تحضير البيئة

```bash
# تأكد من تثبيت XAMPP
# قم بتشغيل Apache و MySQL من لوحة تحكم XAMPP
```

### 2. تحميل الملفات

```bash
# انسخ جميع ملفات المشروع إلى مجلد htdocs في XAMPP
# مثال: C:\xampp\htdocs\smart-communication\
```

### 3. تثبيت التبعيات

```bash
# افتح Command Prompt في مجلد المشروع
cd C:\xampp\htdocs\smart-communication
composer install
```

### 4. إعداد قاعدة البيانات

1. افتح phpMyAdmin من خلال: http://localhost/phpmyadmin
2. أنشئ قاعدة بيانات جديدة باسم `smart_communication`
3. استورد ملف `database/smart_communication.sql`

### 5. تكوين الإعدادات

قم بتعديل ملف `config/database.php` وتحديث إعدادات قاعدة البيانات:

```php
private $host = 'localhost';
private $db_name = 'smart_communication';
private $username = 'root';
private $password = ''; // كلمة مرور MySQL (فارغة افتراضياً في XAMPP)
```

### 6. إعداد البريد الإلكتروني

قم بتحديث إعدادات SMTP في ملف `config/database.php`:

```php
const SMTP_HOST = 'smtp.gmail.com';
const SMTP_PORT = 587;
const SMTP_USERNAME = '<EMAIL>';
const SMTP_PASSWORD = 'your-app-password';
```

### 7. إعداد واتساب (اختياري)

لاستخدام WhatsApp API، قم بتحديث الإعدادات:

```php
const WHATSAPP_API_URL = 'your-whatsapp-api-url';
const WHATSAPP_API_TOKEN = 'your-api-token';
```

## الاستخدام

### تسجيل الدخول الأول

- الرابط: http://localhost/smart-communication
- اسم المستخدم: `admin`
- كلمة المرور: `password` (يجب تغييرها فوراً)

### إضافة موظفين جدد

1. سجل دخول كمدير
2. اذهب إلى "إدارة المستخدمين" > "إضافة مستخدم"
3. املأ البيانات المطلوبة وحدد الصلاحيات

### إضافة عملاء

1. اذهب إلى "إدارة العملاء" > "إضافة عميل جديد"
2. املأ جميع البيانات المطلوبة
3. تأكد من صحة البريد الإلكتروني ورقم واتساب

### إرسال الرسائل

#### البريد الإلكتروني:
1. اذهب إلى "التواصل" > "إرسال بريد إلكتروني"
2. اختر العميل
3. اكتب الموضوع والمحتوى
4. يمكنك استخدام القوالب الجاهزة

#### واتساب:
1. اذهب إلى "التواصل" > "إرسال واتساب"
2. اختر العميل
3. اكتب الرسالة
4. اختر طريقة الإرسال (رابط أو API)

## الأمان

### حماية البيانات
- جميع كلمات المرور مشفرة باستخدام bcrypt
- استخدام Prepared Statements لمنع SQL Injection
- حماية CSRF في جميع النماذج
- تشفير البيانات الحساسة

### النسخ الاحتياطي
- قم بعمل نسخة احتياطية من قاعدة البيانات بانتظام
- احتفظ بنسخة من ملفات النظام

## استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في الاتصال بقاعدة البيانات
```
تأكد من:
- تشغيل MySQL في XAMPP
- صحة اسم قاعدة البيانات
- صحة بيانات الاتصال
```

#### خطأ في إرسال البريد الإلكتروني
```
تأكد من:
- صحة إعدادات SMTP
- تفعيل "Less secure app access" في Gmail
- استخدام App Password بدلاً من كلمة المرور العادية
```

#### مشاكل في واتساب
```
- تأكد من صحة رقم الهاتف
- تحقق من إعدادات API إذا كنت تستخدمها
- استخدم الروابط المباشرة كبديل
```

## الدعم الفني

للحصول على الدعم الفني:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +962-6-1234567

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للمزيد من التفاصيل.

## المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل إرسال Pull Request.

---

© 2024 نظام التواصل الذكي. جميع الحقوق محفوظة.
