<?php
/**
 * صفحة إضافة عميل جديد - نظام التواصل الذكي
 * Add New Client Page - Smart Communication System
 */

require_once 'includes/functions.php';

// بدء الجلسة والتحقق من تسجيل الدخول
startSecureSession();
if (!isLoggedIn()) {
    redirect('login.php');
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['user_role'];

// معالجة إضافة العميل
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrf_token)) {
        showAlert('خطأ في التحقق من الأمان', 'danger');
    } else {
        // جمع البيانات وتنظيفها
        $name = Config::sanitizeInput($_POST['name'] ?? '');
        $organization = Config::sanitizeInput($_POST['organization'] ?? '');
        $sector = Config::sanitizeInput($_POST['sector'] ?? '');
        $email = Config::sanitizeInput($_POST['email'] ?? '');
        $whatsapp_number = Config::sanitizeInput($_POST['whatsapp_number'] ?? '');
        $phone = Config::sanitizeInput($_POST['phone'] ?? '');
        $address = Config::sanitizeInput($_POST['address'] ?? '');
        $priority = Config::sanitizeInput($_POST['priority'] ?? 'medium');
        $status = Config::sanitizeInput($_POST['status'] ?? 'active');
        $assigned_employee_id = (int)($_POST['assigned_employee_id'] ?? 0);
        $notes = Config::sanitizeInput($_POST['notes'] ?? '');
        
        // التحقق من صحة البيانات
        $errors = [];
        
        if (empty($name)) {
            $errors[] = 'اسم العميل مطلوب';
        }
        
        if (empty($organization)) {
            $errors[] = 'اسم المؤسسة مطلوب';
        }
        
        if (empty($sector) || !in_array($sector, ['educational', 'government', 'private'])) {
            $errors[] = 'يجب اختيار قطاع صحيح';
        }
        
        if (!empty($email) && !validateEmail($email)) {
            $errors[] = 'البريد الإلكتروني غير صحيح';
        }
        
        if (!empty($whatsapp_number) && !validatePhone($whatsapp_number)) {
            $errors[] = 'رقم واتساب غير صحيح';
        }
        
        if (!empty($phone) && !validatePhone($phone)) {
            $errors[] = 'رقم الهاتف غير صحيح';
        }
        
        if (!in_array($priority, ['low', 'medium', 'high'])) {
            $errors[] = 'أولوية غير صحيحة';
        }
        
        if (!in_array($status, ['active', 'inactive', 'potential'])) {
            $errors[] = 'حالة غير صحيحة';
        }
        
        // التحقق من صلاحية تعيين الموظف
        if ($assigned_employee_id > 0 && $user_role !== 'admin' && $user_role !== 'supervisor') {
            $assigned_employee_id = $user_id; // تعيين العميل للموظف الحالي
        }
        
        if (empty($errors)) {
            try {
                $db = new Database();
                
                // التحقق من عدم تكرار البريد الإلكتروني
                if (!empty($email)) {
                    $check_query = "SELECT id FROM clients WHERE email = ?";
                    $check_stmt = $db->executeQuery($check_query, [$email]);
                    if ($check_stmt->fetch()) {
                        $errors[] = 'البريد الإلكتروني مستخدم من قبل';
                    }
                }
                
                if (empty($errors)) {
                    // تنسيق أرقام الهاتف
                    if (!empty($whatsapp_number)) {
                        $whatsapp_number = formatPhone($whatsapp_number);
                    }
                    if (!empty($phone)) {
                        $phone = formatPhone($phone);
                    }
                    
                    // إدراج العميل الجديد
                    $insert_query = "INSERT INTO clients 
                                   (name, organization, sector, email, whatsapp_number, phone, 
                                    address, priority, status, assigned_employee_id, notes, created_at) 
                                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                    
                    $params = [
                        $name, $organization, $sector, $email, $whatsapp_number, $phone,
                        $address, $priority, $status, 
                        $assigned_employee_id > 0 ? $assigned_employee_id : null, 
                        $notes
                    ];
                    
                    $db->executeQuery($insert_query, $params);
                    $client_id = $db->getLastInsertId();
                    
                    // تسجيل النشاط
                    logUserActivity($user_id, 'add_client', "إضافة عميل جديد: $name - $organization");
                    
                    showAlert('تم إضافة العميل بنجاح', 'success');
                    redirect("view-client.php?id=$client_id");
                }
                
            } catch (Exception $e) {
                Config::logError("Add client error: " . $e->getMessage());
                $errors[] = 'حدث خطأ في إضافة العميل';
            }
        }
        
        if (!empty($errors)) {
            showAlert(implode('<br>', $errors), 'danger');
        }
    }
}

// الحصول على قائمة الموظفين للتعيين
$employees = [];
if ($user_role === 'admin' || $user_role === 'supervisor') {
    try {
        $db = new Database();
        $employees_query = "SELECT id, full_name FROM users WHERE is_active = TRUE ORDER BY full_name";
        $employees_stmt = $db->executeQuery($employees_query);
        $employees = $employees_stmt->fetchAll();
    } catch (Exception $e) {
        Config::logError("Get employees error: " . $e->getMessage());
    }
}

$page_title = "إضافة عميل جديد";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إضافة عميل جديد</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="clients.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة إلى قائمة العملاء
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user-plus me-2"></i>
                                بيانات العميل الجديد
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" class="needs-validation" novalidate>
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">اسم العميل <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" 
                                               required>
                                        <div class="invalid-feedback">
                                            يرجى إدخال اسم العميل
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="organization" class="form-label">اسم المؤسسة <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="organization" name="organization" 
                                               value="<?php echo htmlspecialchars($_POST['organization'] ?? ''); ?>" 
                                               required>
                                        <div class="invalid-feedback">
                                            يرجى إدخال اسم المؤسسة
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="sector" class="form-label">القطاع <span class="text-danger">*</span></label>
                                        <select class="form-select" id="sector" name="sector" required>
                                            <option value="">اختر القطاع</option>
                                            <option value="educational" <?php echo ($_POST['sector'] ?? '') == 'educational' ? 'selected' : ''; ?>>تعليمي</option>
                                            <option value="government" <?php echo ($_POST['sector'] ?? '') == 'government' ? 'selected' : ''; ?>>حكومي</option>
                                            <option value="private" <?php echo ($_POST['sector'] ?? '') == 'private' ? 'selected' : ''; ?>>خاص</option>
                                        </select>
                                        <div class="invalid-feedback">
                                            يرجى اختيار القطاع
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="priority" class="form-label">الأولوية</label>
                                        <select class="form-select" id="priority" name="priority">
                                            <option value="medium" <?php echo ($_POST['priority'] ?? 'medium') == 'medium' ? 'selected' : ''; ?>>متوسطة</option>
                                            <option value="high" <?php echo ($_POST['priority'] ?? '') == 'high' ? 'selected' : ''; ?>>عالية</option>
                                            <option value="low" <?php echo ($_POST['priority'] ?? '') == 'low' ? 'selected' : ''; ?>>منخفضة</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="status" class="form-label">الحالة</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="active" <?php echo ($_POST['status'] ?? 'active') == 'active' ? 'selected' : ''; ?>>نشط</option>
                                            <option value="potential" <?php echo ($_POST['status'] ?? '') == 'potential' ? 'selected' : ''; ?>>محتمل</option>
                                            <option value="inactive" <?php echo ($_POST['status'] ?? '') == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
                                        <div class="invalid-feedback">
                                            يرجى إدخال بريد إلكتروني صحيح
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="whatsapp_number" class="form-label">رقم واتساب</label>
                                        <input type="tel" class="form-control" id="whatsapp_number" name="whatsapp_number" 
                                               value="<?php echo htmlspecialchars($_POST['whatsapp_number'] ?? ''); ?>"
                                               placeholder="07xxxxxxxx أو +962xxxxxxxx">
                                        <div class="form-text">مثال: 0791234567 أو +962791234567</div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                                               placeholder="07xxxxxxxx أو +962xxxxxxxx">
                                    </div>
                                    
                                    <?php if (!empty($employees)): ?>
                                    <div class="col-md-6 mb-3">
                                        <label for="assigned_employee_id" class="form-label">الموظف المعين</label>
                                        <select class="form-select" id="assigned_employee_id" name="assigned_employee_id">
                                            <option value="">اختر الموظف</option>
                                            <?php foreach ($employees as $employee): ?>
                                            <option value="<?php echo $employee['id']; ?>" 
                                                    <?php echo ($_POST['assigned_employee_id'] ?? '') == $employee['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($employee['full_name']); ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="address" class="form-label">العنوان</label>
                                    <textarea class="form-control" id="address" name="address" rows="2"><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo htmlspecialchars($_POST['notes'] ?? ''); ?></textarea>
                                </div>
                                
                                <div class="d-flex justify-content-between">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ العميل
                                    </button>
                                    <a href="clients.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                إرشادات
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb me-1"></i> نصائح مهمة:</h6>
                                <ul class="mb-0 small">
                                    <li>تأكد من صحة البريد الإلكتروني ورقم واتساب</li>
                                    <li>اختر القطاع المناسب للعميل</li>
                                    <li>حدد الأولوية حسب أهمية العميل</li>
                                    <li>أضف ملاحظات مفيدة للمتابعة</li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle me-1"></i> تنبيه:</h6>
                                <p class="mb-0 small">
                                    الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة ويجب ملؤها
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        const forms = document.getElementsByClassName('needs-validation');
        Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// تنسيق أرقام الهاتف تلقائياً
document.getElementById('whatsapp_number').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.startsWith('962')) {
        value = '+' + value;
    } else if (value.startsWith('0')) {
        value = '+962' + value.substring(1);
    }
    e.target.value = value;
});

document.getElementById('phone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.startsWith('962')) {
        value = '+' + value;
    } else if (value.startsWith('0')) {
        value = '+962' + value.substring(1);
    }
    e.target.value = value;
});
</script>

<?php include 'includes/footer.php'; ?>
