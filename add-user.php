<?php
/**
 * صفحة إضافة مستخدم جديد - نظام التواصل الذكي
 * Add New User Page - Smart Communication System
 */

require_once 'includes/functions.php';
require_once 'includes/Security.php';

// بدء الجلسة والتحقق من تسجيل الدخول
startSecureSession();
if (!isLoggedIn()) {
    redirect('login.php');
}

// التحقق من الصلاحيات (مدير فقط)
if (!hasPermission('admin')) {
    showAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    redirect('index.php');
}

$user_id = $_SESSION['user_id'];

// معالجة إضافة المستخدم
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrf_token)) {
        showAlert('خطأ في التحقق من الأمان', 'danger');
    } else {
        // جمع البيانات وتنظيفها
        $username = Config::sanitizeInput($_POST['username'] ?? '');
        $email = Config::sanitizeInput($_POST['email'] ?? '');
        $full_name = Config::sanitizeInput($_POST['full_name'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $role = Config::sanitizeInput($_POST['role'] ?? 'employee');
        $phone = Config::sanitizeInput($_POST['phone'] ?? '');
        $department = Config::sanitizeInput($_POST['department'] ?? '');
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        
        // التحقق من صحة البيانات
        $errors = [];
        
        if (empty($username)) {
            $errors[] = 'اسم المستخدم مطلوب';
        } elseif (strlen($username) < 3) {
            $errors[] = 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
        } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
            $errors[] = 'اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط';
        }
        
        if (empty($email)) {
            $errors[] = 'البريد الإلكتروني مطلوب';
        } elseif (!Security::validateEmail($email)) {
            $errors[] = 'البريد الإلكتروني غير صحيح';
        }
        
        if (empty($full_name)) {
            $errors[] = 'الاسم الكامل مطلوب';
        }
        
        if (empty($password)) {
            $errors[] = 'كلمة المرور مطلوبة';
        } else {
            $password_validation = Security::validatePassword($password);
            if ($password_validation !== true) {
                $errors = array_merge($errors, $password_validation);
            }
        }
        
        if ($password !== $confirm_password) {
            $errors[] = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين';
        }
        
        if (!in_array($role, ['admin', 'supervisor', 'employee'])) {
            $errors[] = 'الصلاحية غير صحيحة';
        }
        
        if (!empty($phone) && !validatePhone($phone)) {
            $errors[] = 'رقم الهاتف غير صحيح';
        }
        
        if (empty($errors)) {
            try {
                $db = new Database();
                
                // التحقق من عدم تكرار اسم المستخدم
                $check_username_query = "SELECT id FROM users WHERE username = ?";
                $check_username_stmt = $db->executeQuery($check_username_query, [$username]);
                if ($check_username_stmt->fetch()) {
                    $errors[] = 'اسم المستخدم مستخدم من قبل';
                }
                
                // التحقق من عدم تكرار البريد الإلكتروني
                $check_email_query = "SELECT id FROM users WHERE email = ?";
                $check_email_stmt = $db->executeQuery($check_email_query, [$email]);
                if ($check_email_stmt->fetch()) {
                    $errors[] = 'البريد الإلكتروني مستخدم من قبل';
                }
                
                if (empty($errors)) {
                    // تنسيق رقم الهاتف
                    if (!empty($phone)) {
                        $phone = formatPhone($phone);
                    }
                    
                    // تشفير كلمة المرور
                    $hashed_password = Security::hashPassword($password);
                    
                    // إدراج المستخدم الجديد
                    $insert_query = "INSERT INTO users 
                                   (username, email, password, full_name, role, phone, department, is_active, created_at) 
                                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                    
                    $params = [
                        $username, $email, $hashed_password, $full_name, 
                        $role, $phone, $department, $is_active
                    ];
                    
                    $db->executeQuery($insert_query, $params);
                    $new_user_id = $db->getLastInsertId();
                    
                    // تسجيل النشاط
                    logUserActivity($user_id, 'add_user', "إضافة مستخدم جديد: $username ($full_name)");
                    
                    showAlert('تم إضافة المستخدم بنجاح', 'success');
                    redirect("users.php");
                }
                
            } catch (Exception $e) {
                Config::logError("Add user error: " . $e->getMessage());
                $errors[] = 'حدث خطأ في إضافة المستخدم';
            }
        }
        
        if (!empty($errors)) {
            showAlert(implode('<br>', $errors), 'danger');
        }
    }
}

$page_title = "إضافة مستخدم جديد";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إضافة مستخدم جديد</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="users.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة إلى قائمة المستخدمين
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user-plus me-2"></i>
                                بيانات المستخدم الجديد
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" class="needs-validation" novalidate>
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="username" name="username" 
                                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" 
                                               required pattern="[a-zA-Z0-9_]{3,}" 
                                               title="3 أحرف على الأقل، أحرف وأرقام فقط">
                                        <div class="invalid-feedback">
                                            يرجى إدخال اسم مستخدم صحيح (3 أحرف على الأقل)
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                               required>
                                        <div class="invalid-feedback">
                                            يرجى إدخال بريد إلكتروني صحيح
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>" 
                                           required>
                                    <div class="invalid-feedback">
                                        يرجى إدخال الاسم الكامل
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="password" name="password" 
                                                   required minlength="8">
                                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        <div class="form-text">
                                            8 أحرف على الأقل، يجب أن تحتوي على حرف كبير وصغير ورقم ورمز خاص
                                        </div>
                                        <div class="invalid-feedback">
                                            كلمة المرور يجب أن تكون 8 أحرف على الأقل
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                                   required minlength="8">
                                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        <div class="invalid-feedback">
                                            يرجى تأكيد كلمة المرور
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="role" class="form-label">الصلاحية <span class="text-danger">*</span></label>
                                        <select class="form-select" id="role" name="role" required>
                                            <option value="">اختر الصلاحية</option>
                                            <option value="employee" <?php echo ($_POST['role'] ?? '') == 'employee' ? 'selected' : ''; ?>>موظف</option>
                                            <option value="supervisor" <?php echo ($_POST['role'] ?? '') == 'supervisor' ? 'selected' : ''; ?>>مشرف</option>
                                            <option value="admin" <?php echo ($_POST['role'] ?? '') == 'admin' ? 'selected' : ''; ?>>مدير</option>
                                        </select>
                                        <div class="invalid-feedback">
                                            يرجى اختيار الصلاحية
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="phone" class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                                               placeholder="07xxxxxxxx أو +962xxxxxxxx">
                                        <div class="form-text">مثال: 0791234567 أو +962791234567</div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="department" class="form-label">القسم</label>
                                        <input type="text" class="form-control" id="department" name="department" 
                                               value="<?php echo htmlspecialchars($_POST['department'] ?? ''); ?>"
                                               placeholder="مثال: المبيعات، التسويق، الدعم الفني">
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                               <?php echo (isset($_POST['is_active']) || !isset($_POST['username'])) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="is_active">
                                            تفعيل الحساب
                                        </label>
                                        <div class="form-text">إذا لم يتم تفعيل الحساب، لن يتمكن المستخدم من تسجيل الدخول</div>
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-between">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        إضافة المستخدم
                                    </button>
                                    <a href="users.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات الصلاحيات
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-users me-1"></i> أنواع الصلاحيات:</h6>
                                <ul class="mb-0 small">
                                    <li><strong>مدير:</strong> صلاحيات كاملة على النظام</li>
                                    <li><strong>مشرف:</strong> إدارة الموظفين والعملاء</li>
                                    <li><strong>موظف:</strong> التواصل مع العملاء المعينين له</li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle me-1"></i> متطلبات كلمة المرور:</h6>
                                <ul class="mb-0 small">
                                    <li>8 أحرف على الأقل</li>
                                    <li>حرف كبير واحد على الأقل</li>
                                    <li>حرف صغير واحد على الأقل</li>
                                    <li>رقم واحد على الأقل</li>
                                    <li>رمز خاص واحد على الأقل</li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-success">
                                <h6><i class="fas fa-lightbulb me-1"></i> نصائح:</h6>
                                <ul class="mb-0 small">
                                    <li>استخدم أسماء مستخدمين واضحة</li>
                                    <li>تأكد من صحة البريد الإلكتروني</li>
                                    <li>اختر كلمة مرور قوية</li>
                                    <li>حدد الصلاحية المناسبة</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        const forms = document.getElementsByClassName('needs-validation');
        Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// إظهار/إخفاء كلمة المرور
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        button.classList.remove('fa-eye');
        button.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        button.classList.remove('fa-eye-slash');
        button.classList.add('fa-eye');
    }
}

// التحقق من تطابق كلمة المرور
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('كلمة المرور غير متطابقة');
    } else {
        this.setCustomValidity('');
    }
});

// تنسيق رقم الهاتف تلقائياً
document.getElementById('phone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.startsWith('962')) {
        value = '+' + value;
    } else if (value.startsWith('0')) {
        value = '+962' + value.substring(1);
    }
    e.target.value = value;
});
</script>

<?php include 'includes/footer.php'; ?>
