/**
 * ملف التصميم المخصص - نظام التواصل الذكي
 * Custom Styles - Smart Communication System
 */

/* متغيرات الألوان */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 10px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* الخطوط والنصوص */
body {
    font-family: 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif;
    background-color: var(--light-color);
    line-height: 1.6;
}

/* دعم اللغة العربية */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .text-left {
    text-align: right !important;
}

[dir="rtl"] .text-right {
    text-align: left !important;
}

/* شريط التنقل */
.navbar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    box-shadow: var(--box-shadow);
    border: none;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: white !important;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: white !important;
    transform: translateY(-1px);
}

/* الشريط الجانبي */
.sidebar {
    background: white;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 56px);
    position: sticky;
    top: 56px;
}

.sidebar .nav-link {
    color: var(--dark-color);
    padding: 12px 20px;
    border-radius: var(--border-radius);
    margin: 2px 10px;
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.sidebar .nav-link:hover {
    background-color: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
    transform: translateX(-5px);
}

.sidebar .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
    margin-left: 10px;
}

/* البطاقات */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    font-weight: 600;
}

.card-title {
    margin-bottom: 0;
    font-size: 1.1rem;
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

/* الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

/* النماذج */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
}

/* الشارات */
.badge {
    border-radius: 20px;
    padding: 6px 12px;
    font-weight: 500;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

/* النوافذ المنبثقة */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
}

/* القوائم المنسدلة */
.dropdown-menu {
    border: none;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
}

.dropdown-item:hover {
    background-color: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
}

/* الرسوم البيانية */
.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

/* الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.stats-label {
    font-size: 1rem;
    opacity: 0.9;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 56px;
        right: -100%;
        width: 280px;
        height: calc(100vh - 56px);
        transition: right 0.3s ease;
        z-index: 1000;
        overflow-y: auto;
    }
    
    .sidebar.show {
        right: 0;
    }
    
    .sidebar-overlay {
        position: fixed;
        top: 56px;
        right: 0;
        width: 100%;
        height: calc(100vh - 56px);
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        display: none;
    }
    
    .sidebar-overlay.show {
        display: block;
    }
    
    .main-content {
        margin-right: 0 !important;
        padding: 15px;
    }
    
    .card {
        margin-bottom: 20px;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
    
    .btn-group .btn {
        padding: 5px 8px;
        font-size: 0.8rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 10px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .btn {
        font-size: 0.9rem;
        padding: 8px 12px;
    }
    
    .table {
        font-size: 0.8rem;
    }
    
    .form-control, .form-select {
        font-size: 0.9rem;
    }
    
    .stats-number {
        font-size: 1.8rem;
    }
    
    .stats-label {
        font-size: 0.9rem;
    }
}

/* تحسينات للطباعة */
@media print {
    .sidebar, 
    .navbar, 
    .btn, 
    .dropdown,
    .modal,
    .sidebar-overlay {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .table {
        font-size: 12px;
    }
    
    .page-break {
        page-break-before: always;
    }
}

/* تحسينات إضافية */
.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تحسينات للوصولية */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* تحسينات للتركيز */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* تحسينات للحالات */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

/* تحسينات للأيقونات */
.icon-lg {
    font-size: 1.5rem;
}

.icon-xl {
    font-size: 2rem;
}

.icon-xxl {
    font-size: 3rem;
}

/* تحسينات للمسافات */
.mt-6 {
    margin-top: 4rem !important;
}

.mb-6 {
    margin-bottom: 4rem !important;
}

.pt-6 {
    padding-top: 4rem !important;
}

.pb-6 {
    padding-bottom: 4rem !important;
}
