/**
 * ملف JavaScript المخصص - نظام التواصل الذكي
 * Custom JavaScript - Smart Communication System
 */

// إعدادات التطبيق
const SmartComm = {
    // إعدادات عامة
    config: {
        baseUrl: window.location.origin,
        apiUrl: window.location.origin + '/api',
        language: 'ar',
        direction: 'rtl'
    },
    
    // وظائف المساعدة
    utils: {
        // تنسيق التاريخ
        formatDate: function(dateString, format = 'YYYY-MM-DD') {
            const date = new Date(dateString);
            const options = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            };
            
            if (SmartComm.config.language === 'ar') {
                return date.toLocaleDateString('ar-SA', options);
            } else {
                return date.toLocaleDateString('en-US', options);
            }
        },
        
        // تنسيق الأرقام
        formatNumber: function(number) {
            if (SmartComm.config.language === 'ar') {
                return new Intl.NumberFormat('ar-SA').format(number);
            } else {
                return new Intl.NumberFormat('en-US').format(number);
            }
        },
        
        // عرض رسالة تنبيه
        showAlert: function(message, type = 'info', duration = 5000) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            // إزالة التنبيه تلقائياً
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, duration);
        },
        
        // تأكيد الحذف
        confirmDelete: function(message = 'هل أنت متأكد من الحذف؟') {
            return confirm(message);
        },
        
        // تحميل البيانات بـ AJAX
        loadData: function(url, callback, errorCallback = null) {
            fetch(url, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => callback(data))
            .catch(error => {
                console.error('Error:', error);
                if (errorCallback) {
                    errorCallback(error);
                } else {
                    SmartComm.utils.showAlert('حدث خطأ في تحميل البيانات', 'danger');
                }
            });
        },
        
        // إرسال البيانات بـ AJAX
        sendData: function(url, data, callback, errorCallback = null) {
            const formData = new FormData();
            
            // إضافة البيانات
            for (const key in data) {
                if (data.hasOwnProperty(key)) {
                    formData.append(key, data[key]);
                }
            }
            
            fetch(url, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => callback(data))
            .catch(error => {
                console.error('Error:', error);
                if (errorCallback) {
                    errorCallback(error);
                } else {
                    SmartComm.utils.showAlert('حدث خطأ في إرسال البيانات', 'danger');
                }
            });
        },
        
        // تحديث الوقت الحالي
        updateCurrentTime: function() {
            const timeElements = document.querySelectorAll('.current-time');
            const now = new Date();
            const timeString = SmartComm.utils.formatDate(now);
            
            timeElements.forEach(element => {
                element.textContent = timeString;
            });
        },
        
        // تنظيف النص
        sanitizeText: function(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    },
    
    // وظائف الواجهة
    ui: {
        // تهيئة التلميحات
        initTooltips: function() {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        },
        
        // تهيئة النوافذ المنبثقة
        initPopovers: function() {
            const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        },
        
        // تهيئة الجداول القابلة للفرز
        initSortableTables: function() {
            const tables = document.querySelectorAll('.sortable-table');
            tables.forEach(table => {
                const headers = table.querySelectorAll('th[data-sort]');
                headers.forEach(header => {
                    header.style.cursor = 'pointer';
                    header.addEventListener('click', () => SmartComm.ui.sortTable(table, header.dataset.sort));
                });
            });
        },
        
        // فرز الجدول
        sortTable: function(table, column) {
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const isAscending = table.dataset.sortOrder !== 'asc';
            
            rows.sort((a, b) => {
                const aValue = a.querySelector(`[data-sort="${column}"]`)?.textContent || '';
                const bValue = b.querySelector(`[data-sort="${column}"]`)?.textContent || '';
                
                if (isAscending) {
                    return aValue.localeCompare(bValue, SmartComm.config.language);
                } else {
                    return bValue.localeCompare(aValue, SmartComm.config.language);
                }
            });
            
            // إعادة ترتيب الصفوف
            rows.forEach(row => tbody.appendChild(row));
            
            // تحديث اتجاه الفرز
            table.dataset.sortOrder = isAscending ? 'asc' : 'desc';
            
            // تحديث أيقونة الفرز
            const headers = table.querySelectorAll('th[data-sort]');
            headers.forEach(h => h.classList.remove('sort-asc', 'sort-desc'));
            
            const currentHeader = table.querySelector(`th[data-sort="${column}"]`);
            currentHeader.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
        },
        
        // تهيئة النماذج
        initForms: function() {
            const forms = document.querySelectorAll('form[data-ajax="true"]');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const formData = new FormData(form);
                    const url = form.action || window.location.href;
                    
                    SmartComm.utils.sendData(url, Object.fromEntries(formData), function(data) {
                        if (data.success) {
                            SmartComm.utils.showAlert(data.message, 'success');
                            if (data.redirect) {
                                setTimeout(() => {
                                    window.location.href = data.redirect;
                                }, 1500);
                            }
                        } else {
                            SmartComm.utils.showAlert(data.message, 'danger');
                        }
                    });
                });
            });
        },
        
        // تبديل الشريط الجانبي
        toggleSidebar: function() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');
            
            if (sidebar && overlay) {
                sidebar.classList.toggle('show');
                overlay.classList.toggle('show');
            }
        },
        
        // إغلاق الشريط الجانبي
        closeSidebar: function() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');
            
            if (sidebar && overlay) {
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
            }
        }
    },
    
    // وظائف الرسوم البيانية
    charts: {
        // إنشاء رسم بياني خطي
        createLineChart: function(canvasId, data, options = {}) {
            const ctx = document.getElementById(canvasId);
            if (!ctx) return null;
            
            const defaultOptions = {
                responsive: true,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            };
            
            return new Chart(ctx, {
                type: 'line',
                data: data,
                options: { ...defaultOptions, ...options }
            });
        },
        
        // إنشاء رسم بياني دائري
        createPieChart: function(canvasId, data, options = {}) {
            const ctx = document.getElementById(canvasId);
            if (!ctx) return null;
            
            const defaultOptions = {
                responsive: true,
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom'
                    }
                }
            };
            
            return new Chart(ctx, {
                type: 'pie',
                data: data,
                options: { ...defaultOptions, ...options }
            });
        },
        
        // إنشاء رسم بياني عمودي
        createBarChart: function(canvasId, data, options = {}) {
            const ctx = document.getElementById(canvasId);
            if (!ctx) return null;
            
            const defaultOptions = {
                responsive: true,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            };
            
            return new Chart(ctx, {
                type: 'bar',
                data: data,
                options: { ...defaultOptions, ...options }
            });
        }
    },
    
    // تهيئة التطبيق
    init: function() {
        // تهيئة العناصر التفاعلية
        SmartComm.ui.initTooltips();
        SmartComm.ui.initPopovers();
        SmartComm.ui.initSortableTables();
        SmartComm.ui.initForms();
        
        // تحديث الوقت كل دقيقة
        setInterval(SmartComm.utils.updateCurrentTime, 60000);
        SmartComm.utils.updateCurrentTime();
        
        // تهيئة أحداث الشريط الجانبي
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebarOverlay = document.getElementById('sidebarOverlay');
        
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', SmartComm.ui.toggleSidebar);
        }
        
        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', SmartComm.ui.closeSidebar);
        }
        
        // إغلاق الشريط الجانبي عند النقر على رابط في الجوال
        const sidebarLinks = document.querySelectorAll('.sidebar .nav-link');
        sidebarLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (window.innerWidth < 768) {
                    SmartComm.ui.closeSidebar();
                }
            });
        });
        
        // تهيئة التحقق من صحة النماذج
        const forms = document.querySelectorAll('.needs-validation');
        Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
        
        console.log('Smart Communication System initialized successfully');
    }
};

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', SmartComm.init);

// تصدير الكائن للاستخدام العام
window.SmartComm = SmartComm;
