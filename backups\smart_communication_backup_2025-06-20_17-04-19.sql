-- نسخة احتياطية لقاعدة بيانات نظام التواصل الذكي
-- تاريخ الإنشاء: 2025-06-20 17:04:19
-- المستخدم: مدير النظام

SET FOREIGN_KEY_CHECKS = 0;

-- هي<PERSON><PERSON> الجدول `client_communication_summary`
DROP TABLE IF EXISTS `client_communication_summary`;
;

-- بيانات الجدول `client_communication_summary`
INSERT INTO `client_communication_summary` (`client_id`, `client_name`, `client_email`, `client_phone`, `assigned_employee`, `total_communications`, `last_communication`, `client_status`) VALUES
('1', 'صهيب قاسم', '<EMAIL>', '+962789556693', 'مدير النظام', '3', '2025-06-20 16:47:01', 'active'),
('2', 'su<PERSON><PERSON> qase<PERSON>', '<EMAIL>', '+962789556693', NULL, '2', '2025-06-19 18:25:30', 'active'),
('4', 'qasem', '<EMAIL>', '+962789556693', 'مدير النظام', '4', '2025-06-20 16:30:18', 'inactive');

-- هيكل الجدول `clients`
DROP TABLE IF EXISTS `clients`;
CREATE TABLE `clients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `organization` varchar(150) NOT NULL,
  `sector` enum('educational','government','private') NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `whatsapp_number` varchar(20) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `priority` enum('low','medium','high') DEFAULT 'medium',
  `status` enum('active','inactive','potential') DEFAULT 'active',
  `assigned_employee_id` int(11) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_sector` (`sector`),
  KEY `idx_organization` (`organization`),
  KEY `idx_assigned_employee` (`assigned_employee_id`),
  CONSTRAINT `clients_ibfk_1` FOREIGN KEY (`assigned_employee_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول العملاء';

-- بيانات الجدول `clients`
INSERT INTO `clients` (`id`, `name`, `organization`, `sector`, `email`, `whatsapp_number`, `phone`, `address`, `priority`, `status`, `assigned_employee_id`, `notes`, `created_at`, `updated_at`) VALUES
('1', 'صهيب قاسم', 'Rapid Response', 'private', '<EMAIL>', '', '+962789556693', 'الاردن عمان', 'medium', 'active', '1', '', '2025-06-19 18:16:19', '2025-06-19 18:16:19'),
('2', 'suhaib qasem', 'سيبر', 'educational', '<EMAIL>', '+962789556693', '+962789556693', 'الاردن عمان', 'medium', 'active', NULL, '', '2025-06-19 18:20:22', '2025-06-19 18:20:22'),
('4', 'qasem', 'sadaw', 'educational', '<EMAIL>', '+962789556693', '+962789556693', 'الاردن عمان', 'medium', 'inactive', '1', '', '2025-06-20 15:36:10', '2025-06-20 15:36:10');

-- هيكل الجدول `communication_log`
DROP TABLE IF EXISTS `communication_log`;
CREATE TABLE `communication_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `employee_id` int(11) NOT NULL,
  `communication_type` enum('email','whatsapp','phone','meeting') NOT NULL,
  `subject` varchar(200) DEFAULT NULL,
  `message` text DEFAULT NULL,
  `status` enum('sent','delivered','read','replied','failed') DEFAULT 'sent',
  `response` text DEFAULT NULL,
  `scheduled_at` timestamp NULL DEFAULT NULL,
  `sent_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_client_id` (`client_id`),
  KEY `idx_employee_id` (`employee_id`),
  KEY `idx_communication_type` (`communication_type`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `communication_log_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE CASCADE,
  CONSTRAINT `communication_log_ibfk_2` FOREIGN KEY (`employee_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='سجل جميع أنواع التواصل مع العملاء';

-- بيانات الجدول `communication_log`
INSERT INTO `communication_log` (`id`, `client_id`, `employee_id`, `communication_type`, `subject`, `message`, `status`, `response`, `scheduled_at`, `sent_at`, `created_at`, `updated_at`) VALUES
('1', '2', '1', 'whatsapp', NULL, 'مرحباً suhaib qasem
نود متابعة وضع مؤسستك سيبر والاطمئنان على احتياجاتكم.
نحن في خدمتكم دائماً.
مدير النظام - شركتك', 'sent', NULL, NULL, '2025-06-19 18:22:07', '2025-06-19 18:22:07', '2025-06-19 18:22:07'),
('2', '2', '1', 'whatsapp', NULL, 'مرحباً suhaib qasem
نود متابعة وضع مؤسستك سيبر والاطمئنان على احتياجاتكم.
نحن في خدمتكم دائماً.
مدير النظام - شركتك', 'sent', NULL, NULL, '2025-06-19 18:25:30', '2025-06-19 18:25:30', '2025-06-19 18:25:30'),
('3', '1', '1', 'email', 'مرحباً بك في شركتك', 'عزيزي/عزيزتي صهيب قاسم,

نرحب بك في شركتك. نحن سعداء لخدمتك وتقديم أفضل الحلول لمؤسستك Rapid Response.

سنتواصل معك قريباً لمناقشة احتياجاتك.

مع أطيب التحيات,
مدير النظام', 'failed', NULL, NULL, '2025-06-19 19:30:16', '2025-06-19 19:30:16', '2025-06-19 19:30:16'),
('4', '4', '1', 'email', 'مرحباً بك في شركتك', 'عزيزي/عزيزتي qasem,

نرحب بك في شركتك. نحن سعداء لخدمتك وتقديم أفضل الحلول لمؤسستك sadaw.

سنتواصل معك قريباً لمناقشة احتياجاتك.

مع أطيب التحيات,
مدير النظام', 'failed', NULL, NULL, '2025-06-20 15:36:42', '2025-06-20 15:36:42', '2025-06-20 15:36:42'),
('5', '4', '1', 'email', 'مرحباً بك في شركتك', 'عزيزي/عزيزتي qasem,

نرحب بك في شركتك. نحن سعداء لخدمتك وتقديم أفضل الحلول لمؤسستك sadaw.

سنتواصل معك قريباً لمناقشة احتياجاتك.

مع أطيب التحيات,
مدير النظام', 'sent', NULL, NULL, '2025-06-20 15:48:46', '2025-06-20 15:48:46', '2025-06-20 15:48:46'),
('6', '4', '1', 'email', 'مرحباً بك في شركتك', 'عزيزي/عزيزتي qasem,

نرحب بك في شركتك. نحن سعداء لخدمتك وتقديم أفضل الحلول لمؤسستك sadaw.

سنتواصل معك قريباً لمناقشة احتياجاتك.

مع أطيب التحيات,
مدير النظام', 'sent', NULL, NULL, '2025-06-20 15:55:12', '2025-06-20 15:55:12', '2025-06-20 15:55:12'),
('7', '1', '1', 'email', 'مرحباً بك في شركتك', 'عزيزي/عزيزتي صهيب قاسم,

نرحب بك في شركتك. نحن سعداء لخدمتك وتقديم أفضل الحلول لمؤسستك Rapid Response.

سنتواصل معك قريباً لمناقشة احتياجاتك.

مع أطيب التحيات,
مدير النظام', 'sent', NULL, NULL, '2025-06-20 15:57:30', '2025-06-20 15:57:30', '2025-06-20 15:57:30'),
('8', '4', '1', 'email', 'مرحباً بك في', ';dkflhgjkdafsbgjk;g', 'sent', NULL, NULL, '2025-06-20 16:30:18', '2025-06-20 16:30:18', '2025-06-20 16:30:18'),
('9', '1', '1', 'email', 'مرحباً بك في شركتك', 'عزيزي/عزيزتي صهيب قاسم,

نرحب بك في شركتك. نحن سعداء لخدمتك وتقديم أفضل الحلول لمؤسستك Rapid Response.

سنتواصل معك قريباً لمناقشة احتياجاتك.

مع أطيب التحيات,
مدير النظام', 'failed', NULL, NULL, '2025-06-20 16:47:01', '2025-06-20 16:47:01', '2025-06-20 16:47:01');

-- هيكل الجدول `employee_performance`
DROP TABLE IF EXISTS `employee_performance`;
;

-- بيانات الجدول `employee_performance`
INSERT INTO `employee_performance` (`employee_id`, `employee_name`, `department`, `assigned_clients`, `total_communications`, `today_communications`, `email_count`, `whatsapp_count`) VALUES
('3', 'qasem', '', '0', '0', '0', '0', '0');

-- هيكل الجدول `failed_login_attempts`
DROP TABLE IF EXISTS `failed_login_attempts`;
CREATE TABLE `failed_login_attempts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(100) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `attempt_time` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_username` (`username`),
  KEY `idx_ip` (`ip_address`),
  KEY `idx_time` (`attempt_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='سجل محاولات تسجيل الدخول الفاشلة';

-- هيكل الجدول `message_templates`
DROP TABLE IF EXISTS `message_templates`;
CREATE TABLE `message_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `type` enum('email','whatsapp') NOT NULL,
  `subject` varchar(200) DEFAULT NULL,
  `content` text NOT NULL,
  `variables` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`variables`)),
  `created_by` int(11) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_created_by` (`created_by`),
  CONSTRAINT `message_templates_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='قوالب الرسائل المحفوظة';

-- بيانات الجدول `message_templates`
INSERT INTO `message_templates` (`id`, `name`, `type`, `subject`, `content`, `variables`, `created_by`, `is_active`, `created_at`, `updated_at`) VALUES
('1', 'ترحيب بالعميل الجديد', 'email', 'مرحباً بك في {{company_name}}', 'عزيزي/عزيزتي {{client_name}},

نرحب بك في {{company_name}}. نحن سعداء لخدمتك وتقديم أفضل الحلول لمؤسستك {{organization}}.

سنتواصل معك قريباً لمناقشة احتياجاتك.

مع أطيب التحيات,
{{employee_name}}', NULL, '1', '1', '2025-06-19 18:13:09', '2025-06-19 18:13:09'),
('2', 'متابعة العميل', 'whatsapp', '', 'مرحباً {{client_name}}
نود متابعة وضع مؤسستك {{organization}} والاطمئنان على احتياجاتكم.
نحن في خدمتكم دائماً.
{{employee_name}} - {{company_name}}', NULL, '1', '1', '2025-06-19 18:13:09', '2025-06-19 18:13:09');

-- هيكل الجدول `notifications`
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `client_id` int(11) DEFAULT NULL,
  `type` enum('follow_up','reminder','system','alert') NOT NULL,
  `title` varchar(200) NOT NULL,
  `message` text NOT NULL,
  `is_read` tinyint(1) DEFAULT 0,
  `priority` enum('low','medium','high') DEFAULT 'medium',
  `scheduled_for` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `client_id` (`client_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_scheduled_for` (`scheduled_for`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `notifications_ibfk_2` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إشعارات المستخدمين';

-- هيكل الجدول `security_log`
DROP TABLE IF EXISTS `security_log`;
CREATE TABLE `security_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `activity` varchar(100) NOT NULL,
  `details` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_activity` (`activity`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- هيكل الجدول `system_settings`
DROP TABLE IF EXISTS `system_settings`;
CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB AUTO_INCREMENT=64 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إعدادات النظام';

-- بيانات الجدول `system_settings`
INSERT INTO `system_settings` (`id`, `setting_key`, `setting_value`, `description`, `created_at`, `updated_at`) VALUES
('1', 'smtp_host', 'smtp.gmail.com', 'خادم البريد الإلكتروني', '2025-06-19 18:13:09', '2025-06-20 17:03:56'),
('2', 'smtp_port', '587', 'منفذ البريد الإلكتروني', '2025-06-19 18:13:09', '2025-06-20 17:03:56'),
('3', 'smtp_username', '<EMAIL>', 'اسم المستخدم للبريد الإلكتروني', '2025-06-19 18:13:09', '2025-06-20 17:03:56'),
('4', 'smtp_password', 'zrpu becc ssci xwdl', 'كلمة مرور البريد الإلكتروني', '2025-06-19 18:13:09', '2025-06-20 17:03:56'),
('5', 'whatsapp_api_url', '', 'رابط واجهة واتساب API', '2025-06-19 18:13:09', '2025-06-20 17:03:56'),
('6', 'whatsapp_api_token', '', 'رمز واجهة واتساب API', '2025-06-19 18:13:09', '2025-06-20 17:03:56'),
('7', 'system_name', 'نظام التواصل الذكي', 'اسم النظام', '2025-06-19 18:13:09', '2025-06-19 18:13:09'),
('8', 'company_name', 'مؤسسة السرعة المتكاملة للتسويق الالكتروني', 'اسم الشركة', '2025-06-19 18:13:09', '2025-06-20 17:03:56'),
('9', 'follow_up_days', '7', 'عدد الأيام للتذكير بالمتابعة', '2025-06-19 18:13:09', '2025-06-19 18:13:09'),
('10', 'timezone', 'Asia/Amman', 'المنطقة الزمنية', '2025-06-19 18:13:09', '2025-06-20 17:03:56'),
('30', 'company_email', '<EMAIL>', NULL, '2025-06-20 15:00:26', '2025-06-20 17:03:56'),
('31', 'company_phone', '0789556693', NULL, '2025-06-20 15:00:26', '2025-06-20 17:03:56'),
('32', 'company_address', 'معهد السرعة المتكاملة للتسويق الإلكتروني
التسوق والتجزئة
🌐 تسويق إلكتروني سريع متكامل (ISEM)
📈 حلول تسويقية إلكترونية مبتكرة
⚡ نُسُرّع نجاز بتكمام تنقيقة
💻 التصميم،... 
أكثر
شارع المدينة المنورة، عمان، الأردن 11183', NULL, '2025-06-20 15:00:26', '2025-06-20 17:03:56'),
('33', 'company_website', 'https://www.instagram.com/isemhub/?locale=ne_NP&amp;amp;amp;amp;amp;hl=ar', NULL, '2025-06-20 15:00:26', '2025-06-20 17:03:56'),
('34', 'smtp_encryption', 'tls', NULL, '2025-06-20 15:00:26', '2025-06-20 17:03:56'),
('35', 'whatsapp_enabled', '0', NULL, '2025-06-20 15:00:26', '2025-06-20 17:03:56'),
('36', 'session_timeout', '3600', NULL, '2025-06-20 15:00:26', '2025-06-20 17:03:56'),
('37', 'max_login_attempts', '5', NULL, '2025-06-20 15:00:26', '2025-06-20 17:03:56'),
('38', 'lockout_duration', '900', NULL, '2025-06-20 15:00:26', '2025-06-20 17:03:56'),
('39', 'enable_notifications', '1', NULL, '2025-06-20 15:00:26', '2025-06-20 17:03:56'),
('40', 'enable_email_logs', '1', NULL, '2025-06-20 15:00:26', '2025-06-20 17:03:56'),
('41', 'smtp_enabled', 'true', NULL, '2025-06-20 16:44:03', '2025-06-20 16:44:03'),
('45', 'smtp_secure', 'tls', NULL, '2025-06-20 16:44:03', '2025-06-20 16:44:03');

-- هيكل الجدول `user_activity`
DROP TABLE IF EXISTS `user_activity`;
CREATE TABLE `user_activity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `activity_type` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_activity_type` (`activity_type`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `user_activity_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='سجل أنشطة المستخدمين';

-- هيكل الجدول `user_sessions`
DROP TABLE IF EXISTS `user_sessions`;
CREATE TABLE `user_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `session_token` varchar(255) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `login_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `logout_time` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_token` (`session_token`),
  KEY `idx_is_active` (`is_active`),
  CONSTRAINT `user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- بيانات الجدول `user_sessions`
INSERT INTO `user_sessions` (`id`, `user_id`, `session_token`, `ip_address`, `user_agent`, `login_time`, `logout_time`, `is_active`) VALUES
('1', '1', '61406b4683aee1a77f042d2be6e5da9aaa9479be358d3ba2521c4334bf9f3ccd', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-19 18:27:50', '2025-06-19 19:10:06', '0'),
('2', '1', '85b250b6414504f34279dcc51198c38e136ac1a016b95a7b1853582c96294d73', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-19 19:29:48', NULL, '1'),
('3', '1', '0792180e874418c2efa07d71e328c05a937c5ef258c1df7d8df98d430289ef88', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-20 14:50:52', '2025-06-20 14:54:28', '0'),
('4', '1', 'd2d8af62bab62cb18fe11ce930bd8e5af4ba1eb004c18fc1d16b6f564cae4ad4', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-20 14:55:23', NULL, '1'),
('5', '3', '05fbbb027ab215ffbdf3966ea98927e89f78b79b85e6060f24292531f62b88b4', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-20 14:56:29', NULL, '1');

-- هيكل الجدول `users`
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('admin','employee','supervisor') DEFAULT 'employee',
  `phone` varchar(20) DEFAULT NULL,
  `department` varchar(50) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول المستخدمين والموظفين';

-- بيانات الجدول `users`
INSERT INTO `users` (`id`, `username`, `email`, `password`, `full_name`, `role`, `phone`, `department`, `is_active`, `last_login`, `created_at`, `updated_at`) VALUES
('1', 'admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin', '+962789556693', '', '1', '2025-06-20 14:55:23', '2025-06-19 18:13:09', '2025-06-20 14:55:23'),
('3', 'qasem', '<EMAIL>', '$argon2id$v=19$m=65536,t=4,p=3$YU11SnN1V0JNZ3k1bDdIdA$PrPOGbJeTlM5aJZXZMaugVjektXwRoSz6Q+XBSLieQI', 'qasem', 'employee', '', '', '1', '2025-06-20 14:56:29', '2025-06-20 14:53:46', '2025-06-20 14:56:29');

SET FOREIGN_KEY_CHECKS = 1;
