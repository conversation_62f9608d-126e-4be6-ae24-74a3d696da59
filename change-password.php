<?php
/**
 * صفحة تغيير كلمة المرور - نظام التواصل الذكي
 * Change Password Page - Smart Communication System
 */

require_once 'includes/functions.php';
require_once 'includes/Security.php';

// بدء الجلسة والتحقق من تسجيل الدخول
startSecureSession();
if (!isLoggedIn()) {
    redirect('login.php');
}

$user_id = $_SESSION['user_id'];

// معالجة تغيير كلمة المرور
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrf_token)) {
        showAlert('خطأ في التحقق من الأمان', 'danger');
    } else {
        // جمع البيانات
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        // التحقق من صحة البيانات
        $errors = [];
        
        if (empty($current_password)) {
            $errors[] = 'كلمة المرور الحالية مطلوبة';
        }
        
        if (empty($new_password)) {
            $errors[] = 'كلمة المرور الجديدة مطلوبة';
        } else {
            $password_validation = Security::validatePassword($new_password);
            if ($password_validation !== true) {
                $errors = array_merge($errors, $password_validation);
            }
        }
        
        if ($new_password !== $confirm_password) {
            $errors[] = 'كلمة المرور الجديدة وتأكيد كلمة المرور غير متطابقتين';
        }
        
        if ($current_password === $new_password) {
            $errors[] = 'كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية';
        }
        
        if (empty($errors)) {
            try {
                $db = new Database();
                
                // التحقق من كلمة المرور الحالية
                $user_query = "SELECT password FROM users WHERE id = ?";
                $user_stmt = $db->executeQuery($user_query, [$user_id]);
                $user_data = $user_stmt->fetch();
                
                if (!$user_data || !Security::verifyPassword($current_password, $user_data['password'])) {
                    $errors[] = 'كلمة المرور الحالية غير صحيحة';
                } else {
                    // تشفير كلمة المرور الجديدة
                    $hashed_password = Security::hashPassword($new_password);
                    
                    // تحديث كلمة المرور
                    $update_query = "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?";
                    $db->executeQuery($update_query, [$hashed_password, $user_id]);
                    
                    // تسجيل النشاط
                    logUserActivity($user_id, 'change_password', "تغيير كلمة المرور");
                    
                    // تسجيل نشاط أمني
                    Security::logSuspiciousActivity('password_change', 'تغيير كلمة المرور بنجاح', $user_id);
                    
                    showAlert('تم تغيير كلمة المرور بنجاح', 'success');
                    
                    // إعادة تعيين النموذج
                    $_POST = [];
                }
                
            } catch (Exception $e) {
                Config::logError("Change password error: " . $e->getMessage());
                $errors[] = 'حدث خطأ في تغيير كلمة المرور';
            }
        }
        
        if (!empty($errors)) {
            showAlert(implode('<br>', $errors), 'danger');
        }
    }
}

$page_title = "تغيير كلمة المرور";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">تغيير كلمة المرور</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="profile.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة إلى الملف الشخصي
                    </a>
                </div>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-key me-2"></i>
                                تغيير كلمة المرور
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" class="needs-validation" novalidate>
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                
                                <div class="mb-3">
                                    <label for="current_password" class="form-label">كلمة المرور الحالية <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">
                                        يرجى إدخال كلمة المرور الحالية
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="new_password" class="form-label">كلمة المرور الجديدة <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="new_password" name="new_password" 
                                               required minlength="8" onkeyup="checkPasswordStrength()">
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">
                                        كلمة المرور يجب أن تكون 8 أحرف على الأقل
                                    </div>
                                    <div class="form-text">
                                        يجب أن تحتوي على حرف كبير وصغير ورقم ورمز خاص
                                    </div>
                                    <!-- مؤشر قوة كلمة المرور -->
                                    <div class="mt-2">
                                        <div class="progress" style="height: 5px;">
                                            <div class="progress-bar" id="passwordStrength" role="progressbar" style="width: 0%"></div>
                                        </div>
                                        <small id="passwordStrengthText" class="text-muted"></small>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                               required minlength="8">
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">
                                        يرجى تأكيد كلمة المرور الجديدة
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-between">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        تغيير كلمة المرور
                                    </button>
                                    <a href="profile.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>
                                        إلغاء
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-shield-alt me-2"></i>
                                نصائح الأمان
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb me-1"></i> كلمة مرور قوية:</h6>
                                <ul class="mb-0 small">
                                    <li>8 أحرف على الأقل</li>
                                    <li>حرف كبير واحد على الأقل (A-Z)</li>
                                    <li>حرف صغير واحد على الأقل (a-z)</li>
                                    <li>رقم واحد على الأقل (0-9)</li>
                                    <li>رمز خاص واحد على الأقل (!@#$%^&*)</li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle me-1"></i> تجنب:</h6>
                                <ul class="mb-0 small">
                                    <li>استخدام معلومات شخصية</li>
                                    <li>كلمات المرور الشائعة</li>
                                    <li>تكرار نفس كلمة المرور</li>
                                    <li>مشاركة كلمة المرور مع الآخرين</li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check me-1"></i> نصائح إضافية:</h6>
                                <ul class="mb-0 small">
                                    <li>غيّر كلمة المرور بانتظام</li>
                                    <li>استخدم كلمة مرور مختلفة لكل حساب</li>
                                    <li>فعّل المصادقة الثنائية إن أمكن</li>
                                    <li>لا تحفظ كلمة المرور في المتصفح</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        const forms = document.getElementsByClassName('needs-validation');
        Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// إظهار/إخفاء كلمة المرور
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        button.classList.remove('fa-eye');
        button.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        button.classList.remove('fa-eye-slash');
        button.classList.add('fa-eye');
    }
}

// فحص قوة كلمة المرور
function checkPasswordStrength() {
    const password = document.getElementById('new_password').value;
    const strengthBar = document.getElementById('passwordStrength');
    const strengthText = document.getElementById('passwordStrengthText');
    
    let strength = 0;
    let feedback = [];
    
    // طول كلمة المرور
    if (password.length >= 8) {
        strength += 20;
    } else {
        feedback.push('8 أحرف على الأقل');
    }
    
    // حرف كبير
    if (/[A-Z]/.test(password)) {
        strength += 20;
    } else {
        feedback.push('حرف كبير');
    }
    
    // حرف صغير
    if (/[a-z]/.test(password)) {
        strength += 20;
    } else {
        feedback.push('حرف صغير');
    }
    
    // رقم
    if (/[0-9]/.test(password)) {
        strength += 20;
    } else {
        feedback.push('رقم');
    }
    
    // رمز خاص
    if (/[^A-Za-z0-9]/.test(password)) {
        strength += 20;
    } else {
        feedback.push('رمز خاص');
    }
    
    // تحديث شريط القوة
    strengthBar.style.width = strength + '%';
    
    if (strength < 40) {
        strengthBar.className = 'progress-bar bg-danger';
        strengthText.textContent = 'ضعيفة - تحتاج: ' + feedback.join(', ');
        strengthText.className = 'text-danger';
    } else if (strength < 80) {
        strengthBar.className = 'progress-bar bg-warning';
        strengthText.textContent = 'متوسطة - تحتاج: ' + feedback.join(', ');
        strengthText.className = 'text-warning';
    } else {
        strengthBar.className = 'progress-bar bg-success';
        strengthText.textContent = 'قوية';
        strengthText.className = 'text-success';
    }
}

// التحقق من تطابق كلمة المرور
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('كلمة المرور غير متطابقة');
    } else {
        this.setCustomValidity('');
    }
});

// التحقق من أن كلمة المرور الجديدة مختلفة عن الحالية
document.getElementById('new_password').addEventListener('input', function() {
    const currentPassword = document.getElementById('current_password').value;
    const newPassword = this.value;
    
    if (currentPassword && currentPassword === newPassword) {
        this.setCustomValidity('كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية');
    } else {
        this.setCustomValidity('');
    }
});
</script>

<?php include 'includes/footer.php'; ?>
