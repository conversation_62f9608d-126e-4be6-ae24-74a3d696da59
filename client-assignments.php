<?php
/**
 * صفحة تعيين العملاء - نظام التواصل الذكي
 * Client Assignments Page - Smart Communication System
 */

require_once 'includes/functions.php';

// بدء الجلسة والتحقق من تسجيل الدخول
startSecureSession();
if (!isLoggedIn()) {
    redirect('login.php');
}

// التحقق من الصلاحيات (مشرف أو مدير)
if (!hasPermission('supervisor')) {
    showAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    redirect('index.php');
}

$user_id = $_SESSION['user_id'];

// معالجة تعيين العملاء
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrf_token)) {
        showAlert('خطأ في التحقق من الأمان', 'danger');
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action == 'assign_clients') {
            $selected_clients = $_POST['selected_clients'] ?? [];
            $assigned_employee_id = (int)($_POST['assigned_employee_id'] ?? 0);
            
            if (!empty($selected_clients) && $assigned_employee_id > 0) {
                try {
                    $db = new Database();
                    
                    // التحقق من وجود الموظف
                    $employee_query = "SELECT full_name FROM users WHERE id = ? AND role IN ('employee', 'supervisor')";
                    $employee_stmt = $db->executeQuery($employee_query, [$assigned_employee_id]);
                    $employee = $employee_stmt->fetch();
                    
                    if ($employee) {
                        $success_count = 0;
                        
                        foreach ($selected_clients as $client_id) {
                            $client_id = (int)$client_id;
                            if ($client_id > 0) {
                                $update_query = "UPDATE clients SET assigned_employee_id = ?, updated_at = NOW() WHERE id = ?";
                                $db->executeQuery($update_query, [$assigned_employee_id, $client_id]);
                                $success_count++;
                            }
                        }
                        
                        if ($success_count > 0) {
                            logUserActivity($user_id, 'assign_clients', "تعيين $success_count عميل للموظف: {$employee['full_name']}");
                            showAlert("تم تعيين $success_count عميل بنجاح للموظف: {$employee['full_name']}", 'success');
                        }
                    } else {
                        showAlert('الموظف المحدد غير موجود', 'danger');
                    }
                    
                } catch (Exception $e) {
                    Config::logError("Assign clients error: " . $e->getMessage());
                    showAlert('حدث خطأ في تعيين العملاء', 'danger');
                }
            } else {
                showAlert('يرجى اختيار عملاء وموظف للتعيين', 'warning');
            }
        }
        
        elseif ($action == 'unassign_clients') {
            $selected_clients = $_POST['selected_clients'] ?? [];
            
            if (!empty($selected_clients)) {
                try {
                    $db = new Database();
                    $success_count = 0;
                    
                    foreach ($selected_clients as $client_id) {
                        $client_id = (int)$client_id;
                        if ($client_id > 0) {
                            $update_query = "UPDATE clients SET assigned_employee_id = NULL, updated_at = NOW() WHERE id = ?";
                            $db->executeQuery($update_query, [$client_id]);
                            $success_count++;
                        }
                    }
                    
                    if ($success_count > 0) {
                        logUserActivity($user_id, 'unassign_clients', "إلغاء تعيين $success_count عميل");
                        showAlert("تم إلغاء تعيين $success_count عميل بنجاح", 'success');
                    }
                    
                } catch (Exception $e) {
                    Config::logError("Unassign clients error: " . $e->getMessage());
                    showAlert('حدث خطأ في إلغاء تعيين العملاء', 'danger');
                }
            } else {
                showAlert('يرجى اختيار عملاء لإلغاء التعيين', 'warning');
            }
        }
    }
    
    redirect('client-assignments.php');
}

// معاملات البحث والتصفية
$search = Config::sanitizeInput($_GET['search'] ?? '');
$assignment_status = Config::sanitizeInput($_GET['assignment_status'] ?? '');
$assigned_employee = Config::sanitizeInput($_GET['assigned_employee'] ?? '');
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

try {
    $db = new Database();
    
    // بناء استعلام البحث
    $where_conditions = [];
    $params = [];
    
    // البحث النصي
    if (!empty($search)) {
        $where_conditions[] = "(c.name LIKE ? OR c.email LIKE ? OR c.phone LIKE ? OR c.company LIKE ?)";
        $search_param = "%$search%";
        $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
    }
    
    // تصفية حالة التعيين
    if ($assignment_status == 'assigned') {
        $where_conditions[] = "c.assigned_employee_id IS NOT NULL";
    } elseif ($assignment_status == 'unassigned') {
        $where_conditions[] = "c.assigned_employee_id IS NULL";
    }
    
    // تصفية الموظف المعين
    if (!empty($assigned_employee)) {
        $where_conditions[] = "c.assigned_employee_id = ?";
        $params[] = $assigned_employee;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // عدد النتائج الإجمالي
    $count_query = "SELECT COUNT(*) as total FROM clients c $where_clause";
    $count_stmt = $db->executeQuery($count_query, $params);
    $total_clients = $count_stmt->fetch()['total'];
    $total_pages = ceil($total_clients / $per_page);
    
    // استعلام العملاء
    $clients_query = "SELECT c.*, u.full_name as assigned_employee_name, u.department as employee_department
                     FROM clients c 
                     LEFT JOIN users u ON c.assigned_employee_id = u.id
                     $where_clause 
                     ORDER BY c.created_at DESC 
                     LIMIT ? OFFSET ?";
    
    $params[] = $per_page;
    $params[] = $offset;
    
    $clients_stmt = $db->executeQuery($clients_query, $params);
    $clients = $clients_stmt->fetchAll();
    
    // قائمة الموظفين
    $employees_query = "SELECT id, full_name, department FROM users WHERE role IN ('employee', 'supervisor') AND is_active = TRUE ORDER BY full_name";
    $employees_stmt = $db->executeQuery($employees_query);
    $employees = $employees_stmt->fetchAll();
    
    // إحصائيات سريعة
    $stats_query = "SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN assigned_employee_id IS NOT NULL THEN 1 END) as assigned,
        COUNT(CASE WHEN assigned_employee_id IS NULL THEN 1 END) as unassigned,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active
        FROM clients";
    
    $stats_stmt = $db->executeQuery($stats_query);
    $stats = $stats_stmt->fetch();
    
} catch (Exception $e) {
    Config::logError("Client assignments page error: " . $e->getMessage());
    $clients = [];
    $employees = [];
    $total_clients = 0;
    $total_pages = 1;
    $stats = ['total' => 0, 'assigned' => 0, 'unassigned' => 0, 'active' => 0];
}

$page_title = "تعيين العملاء";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">تعيين العملاء للموظفين</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-primary" onclick="assignSelected()">
                            <i class="fas fa-user-plus me-1"></i>
                            تعيين المحدد
                        </button>
                        <button type="button" class="btn btn-sm btn-warning" onclick="unassignSelected()">
                            <i class="fas fa-user-minus me-1"></i>
                            إلغاء تعيين المحدد
                        </button>
                    </div>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="h4 mb-0"><?php echo number_format($stats['total']); ?></div>
                                    <div>إجمالي العملاء</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="h4 mb-0"><?php echo number_format($stats['assigned']); ?></div>
                                    <div>معينين</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="h4 mb-0"><?php echo number_format($stats['unassigned']); ?></div>
                                    <div>غير معينين</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-times fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="h4 mb-0"><?php echo number_format($stats['active']); ?></div>
                                    <div>نشطين</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-plus fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج البحث والتصفية -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="اسم العميل، البريد، الهاتف...">
                        </div>
                        <div class="col-md-3">
                            <label for="assignment_status" class="form-label">حالة التعيين</label>
                            <select class="form-select" id="assignment_status" name="assignment_status">
                                <option value="">جميع العملاء</option>
                                <option value="assigned" <?php echo $assignment_status == 'assigned' ? 'selected' : ''; ?>>معينين</option>
                                <option value="unassigned" <?php echo $assignment_status == 'unassigned' ? 'selected' : ''; ?>>غير معينين</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="assigned_employee" class="form-label">الموظف المعين</label>
                            <select class="form-select" id="assigned_employee" name="assigned_employee">
                                <option value="">جميع الموظفين</option>
                                <?php foreach ($employees as $employee): ?>
                                <option value="<?php echo $employee['id']; ?>" 
                                        <?php echo $assigned_employee == $employee['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($employee['full_name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                                <a href="client-assignments.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول العملاء -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            العملاء (<?php echo number_format($total_clients); ?> عميل)
                        </h5>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            <label class="form-check-label" for="selectAll">
                                تحديد الكل
                            </label>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <form id="assignmentForm" method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" id="formAction">
                        <input type="hidden" name="assigned_employee_id" id="assignedEmployeeId">
                        
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="selectAllHeader" onchange="toggleSelectAll()">
                                        </th>
                                        <th>العميل</th>
                                        <th>معلومات الاتصال</th>
                                        <th>الشركة</th>
                                        <th>الموظف المعين</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإضافة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($clients)): ?>
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                            <p class="text-muted">لا توجد عملاء مطابقة لمعايير البحث</p>
                                        </td>
                                    </tr>
                                    <?php else: ?>
                                    <?php foreach ($clients as $client): ?>
                                    <tr>
                                        <td>
                                            <input type="checkbox" name="selected_clients[]" value="<?php echo $client['id']; ?>" 
                                                   class="client-checkbox">
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($client['name']); ?></strong>
                                        </td>
                                        <td>
                                            <?php if (!empty($client['email'])): ?>
                                            <div><i class="fas fa-envelope text-info me-1"></i><?php echo htmlspecialchars($client['email']); ?></div>
                                            <?php endif; ?>
                                            <?php if (!empty($client['phone'])): ?>
                                            <div><i class="fas fa-phone text-success me-1"></i><?php echo htmlspecialchars($client['phone']); ?></div>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($client['company'] ?? 'غير محدد'); ?></td>
                                        <td>
                                            <?php if ($client['assigned_employee_name']): ?>
                                            <span class="badge bg-success">
                                                <?php echo htmlspecialchars($client['assigned_employee_name']); ?>
                                            </span>
                                            <?php if ($client['employee_department']): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($client['employee_department']); ?></small>
                                            <?php endif; ?>
                                            <?php else: ?>
                                            <span class="badge bg-warning">غير معين</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $status_badges = [
                                                'active' => '<span class="badge bg-success">نشط</span>',
                                                'inactive' => '<span class="badge bg-secondary">غير نشط</span>',
                                                'potential' => '<span class="badge bg-warning">محتمل</span>'
                                            ];
                                            echo $status_badges[$client['status']] ?? $client['status'];
                                            ?>
                                        </td>
                                        <td><?php echo formatDateArabic($client['created_at']); ?></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary" 
                                                        onclick="assignSingleClient(<?php echo $client['id']; ?>)" 
                                                        title="تعيين">
                                                    <i class="fas fa-user-plus"></i>
                                                </button>
                                                <?php if ($client['assigned_employee_id']): ?>
                                                <button type="button" class="btn btn-outline-warning" 
                                                        onclick="unassignSingleClient(<?php echo $client['id']; ?>)" 
                                                        title="إلغاء تعيين">
                                                    <i class="fas fa-user-minus"></i>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </form>
                </div>
                
                <!-- ترقيم الصفحات -->
                <?php if ($total_pages > 1): ?>
                <div class="card-footer">
                    <nav aria-label="ترقيم الصفحات">
                        <ul class="pagination justify-content-center mb-0">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">السابق</a>
                            </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                            </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">التالي</a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
</div>

<!-- نموذج تعيين العملاء -->
<div class="modal fade" id="assignModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعيين العملاء</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="employeeSelect" class="form-label">اختر الموظف</label>
                    <select class="form-select" id="employeeSelect" required>
                        <option value="">اختر موظف...</option>
                        <?php foreach ($employees as $employee): ?>
                        <option value="<?php echo $employee['id']; ?>">
                            <?php echo htmlspecialchars($employee['full_name']); ?>
                            <?php if ($employee['department']): ?>
                            - <?php echo htmlspecialchars($employee['department']); ?>
                            <?php endif; ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div id="selectedClientsInfo"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="confirmAssignment()">تعيين العملاء</button>
            </div>
        </div>
    </div>
</div>

<!-- نموذج إلغاء التعيين -->
<div class="modal fade" id="unassignModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إلغاء تعيين العملاء</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من إلغاء تعيين العملاء المحددين؟</p>
                <div id="unassignClientsInfo"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="confirmUnassignment()">إلغاء التعيين</button>
            </div>
        </div>
    </div>
</div>

<script>
// تحديد/إلغاء تحديد جميع العملاء
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const selectAllHeader = document.getElementById('selectAllHeader');
    const checkboxes = document.querySelectorAll('.client-checkbox');
    
    // مزامنة checkbox الرأس مع checkbox الأساسي
    selectAll.checked = selectAllHeader.checked;
    selectAllHeader.checked = selectAll.checked;
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// تعيين العملاء المحددين
function assignSelected() {
    const selected = getSelectedClients();
    if (selected.length === 0) {
        alert('يرجى اختيار عملاء للتعيين');
        return;
    }
    
    document.getElementById('selectedClientsInfo').innerHTML = 
        `<div class="alert alert-info">سيتم تعيين ${selected.length} عميل للموظف المحدد</div>`;
    
    const modal = new bootstrap.Modal(document.getElementById('assignModal'));
    modal.show();
}

// إلغاء تعيين العملاء المحددين
function unassignSelected() {
    const selected = getSelectedClients();
    if (selected.length === 0) {
        alert('يرجى اختيار عملاء لإلغاء التعيين');
        return;
    }
    
    document.getElementById('unassignClientsInfo').innerHTML = 
        `<div class="alert alert-warning">سيتم إلغاء تعيين ${selected.length} عميل</div>`;
    
    const modal = new bootstrap.Modal(document.getElementById('unassignModal'));
    modal.show();
}

// تعيين عميل واحد
function assignSingleClient(clientId) {
    // تحديد العميل
    const checkbox = document.querySelector(`input[value="${clientId}"]`);
    checkbox.checked = true;
    
    assignSelected();
}

// إلغاء تعيين عميل واحد
function unassignSingleClient(clientId) {
    // تحديد العميل
    const checkbox = document.querySelector(`input[value="${clientId}"]`);
    checkbox.checked = true;
    
    unassignSelected();
}

// الحصول على العملاء المحددين
function getSelectedClients() {
    const checkboxes = document.querySelectorAll('.client-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// تأكيد التعيين
function confirmAssignment() {
    const employeeId = document.getElementById('employeeSelect').value;
    if (!employeeId) {
        alert('يرجى اختيار موظف');
        return;
    }
    
    document.getElementById('formAction').value = 'assign_clients';
    document.getElementById('assignedEmployeeId').value = employeeId;
    document.getElementById('assignmentForm').submit();
}

// تأكيد إلغاء التعيين
function confirmUnassignment() {
    document.getElementById('formAction').value = 'unassign_clients';
    document.getElementById('assignmentForm').submit();
}
</script>

<?php include 'includes/footer.php'; ?>
