<?php
/**
 * صفحة تقارير العملاء - نظام التواصل الذكي
 * Client Reports Page - Smart Communication System
 */

require_once 'includes/functions.php';

// بدء الجلسة والتحقق من تسجيل الدخول
startSecureSession();
if (!isLoggedIn()) {
    redirect('login.php');
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['user_role'];

// معاملات التصفية
$date_from = Config::sanitizeInput($_GET['date_from'] ?? date('Y-m-01'));
$date_to = Config::sanitizeInput($_GET['date_to'] ?? date('Y-m-d'));
$status = Config::sanitizeInput($_GET['status'] ?? '');
$assigned_employee = Config::sanitizeInput($_GET['assigned_employee'] ?? '');
$report_type = Config::sanitizeInput($_GET['report_type'] ?? 'summary');

try {
    $db = new Database();
    
    // بناء شروط الاستعلام
    $where_conditions = ["1=1"];
    $params = [];
    
    // تصفية حسب الموظف المعين (للموظفين العاديين)
    if ($user_role == 'employee') {
        $where_conditions[] = "c.assigned_employee_id = ?";
        $params[] = $user_id;
    } elseif (!empty($assigned_employee)) {
        $where_conditions[] = "c.assigned_employee_id = ?";
        $params[] = $assigned_employee;
    }
    
    // تصفية حسب الحالة
    if (!empty($status)) {
        $where_conditions[] = "c.status = ?";
        $params[] = $status;
    }
    
    // تصفية حسب التاريخ (للعملاء المضافين في الفترة)
    if (!empty($date_from)) {
        $where_conditions[] = "DATE(c.created_at) >= ?";
        $params[] = $date_from;
    }
    
    if (!empty($date_to)) {
        $where_conditions[] = "DATE(c.created_at) <= ?";
        $params[] = $date_to;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // تقرير ملخص العملاء
    if ($report_type == 'summary') {
        $summary_query = "SELECT 
            COUNT(*) as total_clients,
            COUNT(CASE WHEN c.status = 'active' THEN 1 END) as active_clients,
            COUNT(CASE WHEN c.status = 'inactive' THEN 1 END) as inactive_clients,
            COUNT(CASE WHEN c.status = 'potential' THEN 1 END) as potential_clients,
            COUNT(CASE WHEN c.assigned_employee_id IS NOT NULL THEN 1 END) as assigned_clients,
            COUNT(CASE WHEN c.assigned_employee_id IS NULL THEN 1 END) as unassigned_clients
            FROM clients c 
            LEFT JOIN users u ON c.assigned_employee_id = u.id
            WHERE $where_clause";
        
        $summary_stmt = $db->executeQuery($summary_query, $params);
        $summary_data = $summary_stmt->fetch();
        
        // تفاصيل العملاء
        $clients_query = "SELECT 
            c.*,
            u.full_name as assigned_employee_name,
            COUNT(cl.id) as communication_count,
            MAX(cl.created_at) as last_communication
            FROM clients c 
            LEFT JOIN users u ON c.assigned_employee_id = u.id
            LEFT JOIN communication_log cl ON c.id = cl.client_id
            WHERE $where_clause
            GROUP BY c.id
            ORDER BY c.created_at DESC";
        
        $clients_stmt = $db->executeQuery($clients_query, $params);
        $clients_data = $clients_stmt->fetchAll();
    }
    
    // تقرير التواصل مع العملاء
    elseif ($report_type == 'communication') {
        $communication_query = "SELECT 
            c.name as client_name,
            c.email as client_email,
            c.phone as client_phone,
            u.full_name as employee_name,
            COUNT(cl.id) as total_communications,
            COUNT(CASE WHEN cl.communication_type = 'email' THEN 1 END) as email_count,
            COUNT(CASE WHEN cl.communication_type = 'whatsapp' THEN 1 END) as whatsapp_count,
            COUNT(CASE WHEN cl.communication_type = 'phone' THEN 1 END) as phone_count,
            MAX(cl.created_at) as last_communication,
            MIN(cl.created_at) as first_communication
            FROM clients c 
            LEFT JOIN users u ON c.assigned_employee_id = u.id
            LEFT JOIN communication_log cl ON c.id = cl.client_id
            WHERE $where_clause AND cl.created_at BETWEEN ? AND ?
            GROUP BY c.id
            HAVING total_communications > 0
            ORDER BY total_communications DESC";
        
        $comm_params = array_merge($params, [$date_from . ' 00:00:00', $date_to . ' 23:59:59']);
        $communication_stmt = $db->executeQuery($communication_query, $comm_params);
        $communication_data = $communication_stmt->fetchAll();
    }
    
    // تقرير العملاء حسب الموظف
    elseif ($report_type == 'by_employee') {
        $employee_query = "SELECT 
            u.full_name as employee_name,
            u.department,
            COUNT(c.id) as total_clients,
            COUNT(CASE WHEN c.status = 'active' THEN 1 END) as active_clients,
            COUNT(CASE WHEN c.status = 'potential' THEN 1 END) as potential_clients,
            COUNT(cl.id) as total_communications,
            MAX(cl.created_at) as last_communication
            FROM users u
            LEFT JOIN clients c ON u.id = c.assigned_employee_id AND $where_clause
            LEFT JOIN communication_log cl ON c.id = cl.client_id
            WHERE u.role IN ('employee', 'supervisor')
            GROUP BY u.id
            ORDER BY total_clients DESC";
        
        $employee_stmt = $db->executeQuery($employee_query, $params);
        $employee_data = $employee_stmt->fetchAll();
    }
    
    // قائمة الموظفين للتصفية
    $employees_query = "SELECT id, full_name FROM users WHERE role IN ('employee', 'supervisor') ORDER BY full_name";
    $employees_stmt = $db->executeQuery($employees_query);
    $employees = $employees_stmt->fetchAll();
    
} catch (Exception $e) {
    Config::logError("Client reports error: " . $e->getMessage());
    showAlert('حدث خطأ في تحميل التقارير', 'danger');
    $summary_data = [];
    $clients_data = [];
    $communication_data = [];
    $employee_data = [];
    $employees = [];
}

$page_title = "تقارير العملاء";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">تقارير العملاء</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="exportReport()">
                            <i class="fas fa-download me-1"></i>
                            تصدير
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>
                            طباعة
                        </button>
                    </div>
                </div>
            </div>

            <!-- نموذج التصفية -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-2">
                            <label for="report_type" class="form-label">نوع التقرير</label>
                            <select class="form-select" id="report_type" name="report_type">
                                <option value="summary" <?php echo $report_type == 'summary' ? 'selected' : ''; ?>>ملخص العملاء</option>
                                <option value="communication" <?php echo $report_type == 'communication' ? 'selected' : ''; ?>>تقرير التواصل</option>
                                <option value="by_employee" <?php echo $report_type == 'by_employee' ? 'selected' : ''; ?>>حسب الموظف</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="active" <?php echo $status == 'active' ? 'selected' : ''; ?>>نشط</option>
                                <option value="inactive" <?php echo $status == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                                <option value="potential" <?php echo $status == 'potential' ? 'selected' : ''; ?>>محتمل</option>
                            </select>
                        </div>
                        <?php if ($user_role != 'employee'): ?>
                        <div class="col-md-2">
                            <label for="assigned_employee" class="form-label">الموظف</label>
                            <select class="form-select" id="assigned_employee" name="assigned_employee">
                                <option value="">جميع الموظفين</option>
                                <?php foreach ($employees as $employee): ?>
                                <option value="<?php echo $employee['id']; ?>" 
                                        <?php echo $assigned_employee == $employee['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($employee['full_name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <?php endif; ?>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>
                                    عرض التقرير
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <?php if ($report_type == 'summary'): ?>
            <!-- ملخص العملاء -->
            <div class="row mb-4">
                <div class="col-md-2 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($summary_data['total_clients'] ?? 0); ?></div>
                            <div>إجمالي العملاء</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($summary_data['active_clients'] ?? 0); ?></div>
                            <div>عملاء نشطين</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($summary_data['potential_clients'] ?? 0); ?></div>
                            <div>عملاء محتملين</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($summary_data['inactive_clients'] ?? 0); ?></div>
                            <div>غير نشطين</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($summary_data['assigned_clients'] ?? 0); ?></div>
                            <div>معينين</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($summary_data['unassigned_clients'] ?? 0); ?></div>
                            <div>غير معينين</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول تفاصيل العملاء -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">تفاصيل العملاء</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم العميل</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الهاتف</th>
                                    <th>الشركة</th>
                                    <th>الموظف المعين</th>
                                    <th>الحالة</th>
                                    <th>عدد التواصل</th>
                                    <th>آخر تواصل</th>
                                    <th>تاريخ الإضافة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($clients_data)): ?>
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد بيانات عملاء للفترة المحددة</p>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($clients_data as $client): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($client['name']); ?></strong></td>
                                    <td><?php echo htmlspecialchars($client['email'] ?? ''); ?></td>
                                    <td><?php echo htmlspecialchars($client['phone'] ?? ''); ?></td>
                                    <td><?php echo htmlspecialchars($client['company'] ?? 'غير محدد'); ?></td>
                                    <td><?php echo htmlspecialchars($client['assigned_employee_name'] ?? 'غير معين'); ?></td>
                                    <td>
                                        <?php
                                        $status_badges = [
                                            'active' => '<span class="badge bg-success">نشط</span>',
                                            'inactive' => '<span class="badge bg-secondary">غير نشط</span>',
                                            'potential' => '<span class="badge bg-warning">محتمل</span>'
                                        ];
                                        echo $status_badges[$client['status']] ?? $client['status'];
                                        ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?php echo number_format($client['communication_count']); ?></span>
                                    </td>
                                    <td>
                                        <?php echo $client['last_communication'] ? formatDateArabic($client['last_communication']) : 'لا يوجد'; ?>
                                    </td>
                                    <td><?php echo formatDateArabic($client['created_at']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <?php elseif ($report_type == 'communication'): ?>
            <!-- تقرير التواصل -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">تقرير التواصل مع العملاء</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>العميل</th>
                                    <th>الموظف المسؤول</th>
                                    <th>إجمالي التواصل</th>
                                    <th>بريد إلكتروني</th>
                                    <th>واتساب</th>
                                    <th>هاتف</th>
                                    <th>أول تواصل</th>
                                    <th>آخر تواصل</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($communication_data)): ?>
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد بيانات تواصل للفترة المحددة</p>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($communication_data as $comm): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($comm['client_name']); ?></strong>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($comm['client_email'] ?? ''); ?></small>
                                    </td>
                                    <td><?php echo htmlspecialchars($comm['employee_name'] ?? 'غير معين'); ?></td>
                                    <td><span class="badge bg-primary fs-6"><?php echo number_format($comm['total_communications']); ?></span></td>
                                    <td><span class="badge bg-info"><?php echo number_format($comm['email_count']); ?></span></td>
                                    <td><span class="badge bg-success"><?php echo number_format($comm['whatsapp_count']); ?></span></td>
                                    <td><span class="badge bg-warning"><?php echo number_format($comm['phone_count']); ?></span></td>
                                    <td><?php echo formatDateArabic($comm['first_communication']); ?></td>
                                    <td><?php echo formatDateArabic($comm['last_communication']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <?php elseif ($report_type == 'by_employee'): ?>
            <!-- تقرير العملاء حسب الموظف -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">تقرير العملاء حسب الموظف</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>الموظف</th>
                                    <th>القسم</th>
                                    <th>إجمالي العملاء</th>
                                    <th>عملاء نشطين</th>
                                    <th>عملاء محتملين</th>
                                    <th>إجمالي التواصل</th>
                                    <th>آخر تواصل</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($employee_data)): ?>
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد بيانات موظفين</p>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($employee_data as $emp): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($emp['employee_name']); ?></strong></td>
                                    <td><?php echo htmlspecialchars($emp['department'] ?? 'غير محدد'); ?></td>
                                    <td><span class="badge bg-primary fs-6"><?php echo number_format($emp['total_clients']); ?></span></td>
                                    <td><span class="badge bg-success"><?php echo number_format($emp['active_clients']); ?></span></td>
                                    <td><span class="badge bg-warning"><?php echo number_format($emp['potential_clients']); ?></span></td>
                                    <td><span class="badge bg-info"><?php echo number_format($emp['total_communications']); ?></span></td>
                                    <td><?php echo $emp['last_communication'] ? formatDateArabic($emp['last_communication']) : 'لا يوجد'; ?></td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </main>
    </div>
</div>

<script>
function exportReport() {
    // تصدير التقرير كـ CSV
    const table = document.querySelector('.table');
    if (!table) return;
    
    let csv = '';
    const rows = table.querySelectorAll('tr');
    
    for (let i = 0; i < rows.length; i++) {
        const cols = rows[i].querySelectorAll('td, th');
        const rowData = [];
        
        for (let j = 0; j < cols.length; j++) {
            let cellData = cols[j].textContent.trim();
            cellData = cellData.replace(/"/g, '""'); // escape quotes
            rowData.push('"' + cellData + '"');
        }
        
        csv += rowData.join(',') + '\n';
    }
    
    // تحميل الملف
    const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'client_report_<?php echo date('Y-m-d'); ?>.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// تحديث التقرير عند تغيير النوع
document.getElementById('report_type').addEventListener('change', function() {
    this.form.submit();
});
</script>

<?php include 'includes/footer.php'; ?>
