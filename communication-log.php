<?php
/**
 * صفحة سجل التواصل - نظام التواصل الذكي
 * Communication Log Page - Smart Communication System
 */

require_once 'includes/functions.php';

// بدء الجلسة والتحقق من تسجيل الدخول
startSecureSession();
if (!isLoggedIn()) {
    redirect('login.php');
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['user_role'];

// معاملات البحث والتصفية
$search = Config::sanitizeInput($_GET['search'] ?? '');
$communication_type = Config::sanitizeInput($_GET['communication_type'] ?? '');
$status = Config::sanitizeInput($_GET['status'] ?? '');
$date_from = Config::sanitizeInput($_GET['date_from'] ?? '');
$date_to = Config::sanitizeInput($_GET['date_to'] ?? '');
$client_id = (int)($_GET['client_id'] ?? 0);
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

try {
    $db = new Database();
    
    // بناء استعلام البحث
    $where_conditions = [];
    $params = [];
    
    // تقييد النتائج حسب الموظف إذا لم يكن مديراً
    if ($user_role !== 'admin') {
        $where_conditions[] = "cl.employee_id = ?";
        $params[] = $user_id;
    }
    
    // البحث النصي
    if (!empty($search)) {
        $where_conditions[] = "(c.name LIKE ? OR c.organization LIKE ? OR cl.subject LIKE ? OR cl.message LIKE ?)";
        $search_param = "%$search%";
        $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
    }
    
    // تصفية نوع التواصل
    if (!empty($communication_type)) {
        $where_conditions[] = "cl.communication_type = ?";
        $params[] = $communication_type;
    }
    
    // تصفية الحالة
    if (!empty($status)) {
        $where_conditions[] = "cl.status = ?";
        $params[] = $status;
    }
    
    // تصفية التاريخ من
    if (!empty($date_from)) {
        $where_conditions[] = "DATE(cl.created_at) >= ?";
        $params[] = $date_from;
    }
    
    // تصفية التاريخ إلى
    if (!empty($date_to)) {
        $where_conditions[] = "DATE(cl.created_at) <= ?";
        $params[] = $date_to;
    }
    
    // تصفية العميل
    if ($client_id > 0) {
        $where_conditions[] = "cl.client_id = ?";
        $params[] = $client_id;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // عدد النتائج الإجمالي
    $count_query = "SELECT COUNT(*) as total 
                   FROM communication_log cl 
                   JOIN clients c ON cl.client_id = c.id 
                   JOIN users u ON cl.employee_id = u.id 
                   $where_clause";
    
    $count_stmt = $db->executeQuery($count_query, $params);
    $total_communications = $count_stmt->fetch()['total'];
    $total_pages = ceil($total_communications / $per_page);
    
    // استعلام سجل التواصل
    $communications_query = "SELECT cl.*, c.name as client_name, c.organization, 
                           u.full_name as employee_name 
                           FROM communication_log cl 
                           JOIN clients c ON cl.client_id = c.id 
                           JOIN users u ON cl.employee_id = u.id 
                           $where_clause 
                           ORDER BY cl.created_at DESC 
                           LIMIT ? OFFSET ?";
    
    $params[] = $per_page;
    $params[] = $offset;
    
    $communications_stmt = $db->executeQuery($communications_query, $params);
    $communications = $communications_stmt->fetchAll();
    
    // إحصائيات سريعة
    $stats_query = "SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN communication_type = 'email' THEN 1 END) as email_count,
        COUNT(CASE WHEN communication_type = 'whatsapp' THEN 1 END) as whatsapp_count,
        COUNT(CASE WHEN communication_type = 'phone' THEN 1 END) as phone_count,
        COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_count,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
        COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_count
        FROM communication_log cl";
    
    if ($user_role !== 'admin') {
        $stats_query .= " WHERE employee_id = ?";
        $stats_stmt = $db->executeQuery($stats_query, [$user_id]);
    } else {
        $stats_stmt = $db->executeQuery($stats_query);
    }
    
    $stats = $stats_stmt->fetch();
    
    // قائمة العملاء للتصفية
    $clients_query = "SELECT DISTINCT c.id, c.name, c.organization 
                     FROM clients c 
                     JOIN communication_log cl ON c.id = cl.client_id";
    
    if ($user_role !== 'admin') {
        $clients_query .= " WHERE cl.employee_id = ?";
        $clients_stmt = $db->executeQuery($clients_query, [$user_id]);
    } else {
        $clients_stmt = $db->executeQuery($clients_query);
    }
    
    $clients_list = $clients_stmt->fetchAll();
    
} catch (Exception $e) {
    Config::logError("Communication log error: " . $e->getMessage());
    $communications = [];
    $total_communications = 0;
    $total_pages = 1;
    $stats = ['total' => 0, 'email_count' => 0, 'whatsapp_count' => 0, 'phone_count' => 0, 'sent_count' => 0, 'failed_count' => 0, 'today_count' => 0];
    $clients_list = [];
}

$page_title = "سجل التواصل";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">سجل التواصل</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="send-email.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-envelope me-1"></i>
                            إرسال بريد
                        </a>
                        <a href="send-whatsapp.php" class="btn btn-sm btn-success">
                            <i class="fab fa-whatsapp me-1"></i>
                            إرسال واتساب
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>
                            طباعة
                        </button>
                    </div>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-2 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <div class="h4 mb-0"><?php echo number_format($stats['total']); ?></div>
                            <div class="small">إجمالي التواصل</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <div class="h4 mb-0"><?php echo number_format($stats['email_count']); ?></div>
                            <div class="small">بريد إلكتروني</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <div class="h4 mb-0"><?php echo number_format($stats['whatsapp_count']); ?></div>
                            <div class="small">واتساب</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <div class="h4 mb-0"><?php echo number_format($stats['phone_count']); ?></div>
                            <div class="small">هاتف</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <div class="h4 mb-0"><?php echo number_format($stats['sent_count']); ?></div>
                            <div class="small">مرسل</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-dark text-white">
                        <div class="card-body text-center">
                            <div class="h4 mb-0"><?php echo number_format($stats['today_count']); ?></div>
                            <div class="small">اليوم</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج البحث والتصفية -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="العميل، الموضوع، المحتوى...">
                        </div>
                        <div class="col-md-2">
                            <label for="communication_type" class="form-label">نوع التواصل</label>
                            <select class="form-select" id="communication_type" name="communication_type">
                                <option value="">جميع الأنواع</option>
                                <option value="email" <?php echo $communication_type == 'email' ? 'selected' : ''; ?>>بريد إلكتروني</option>
                                <option value="whatsapp" <?php echo $communication_type == 'whatsapp' ? 'selected' : ''; ?>>واتساب</option>
                                <option value="phone" <?php echo $communication_type == 'phone' ? 'selected' : ''; ?>>هاتف</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="sent" <?php echo $status == 'sent' ? 'selected' : ''; ?>>مرسل</option>
                                <option value="delivered" <?php echo $status == 'delivered' ? 'selected' : ''; ?>>تم التسليم</option>
                                <option value="read" <?php echo $status == 'read' ? 'selected' : ''; ?>>مقروء</option>
                                <option value="replied" <?php echo $status == 'replied' ? 'selected' : ''; ?>>تم الرد</option>
                                <option value="failed" <?php echo $status == 'failed' ? 'selected' : ''; ?>>فشل</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-1">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="fas fa-search"></i>
                                </button>
                                <a href="communication-log.php" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول سجل التواصل -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        سجل التواصل (<?php echo number_format($total_communications); ?> سجل)
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>التاريخ والوقت</th>
                                    <th>العميل</th>
                                    <th>نوع التواصل</th>
                                    <th>الموضوع/المحتوى</th>
                                    <th>الموظف</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($communications)): ?>
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد سجلات تواصل مطابقة لمعايير البحث</p>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($communications as $comm): ?>
                                <tr>
                                    <td>
                                        <div class="small">
                                            <?php echo formatDateArabic($comm['created_at']); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($comm['client_name']); ?></strong>
                                        </div>
                                        <div class="small text-muted">
                                            <?php echo htmlspecialchars($comm['organization']); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($comm['communication_type'] == 'email'): ?>
                                            <span class="badge bg-primary">
                                                <i class="fas fa-envelope me-1"></i>
                                                بريد إلكتروني
                                            </span>
                                        <?php elseif ($comm['communication_type'] == 'whatsapp'): ?>
                                            <span class="badge bg-success">
                                                <i class="fab fa-whatsapp me-1"></i>
                                                واتساب
                                            </span>
                                        <?php elseif ($comm['communication_type'] == 'phone'): ?>
                                            <span class="badge bg-info">
                                                <i class="fas fa-phone me-1"></i>
                                                هاتف
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">
                                                <?php echo htmlspecialchars($comm['communication_type']); ?>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($comm['subject'])): ?>
                                        <div class="fw-bold">
                                            <?php echo truncateText(htmlspecialchars($comm['subject']), 50); ?>
                                        </div>
                                        <?php endif; ?>
                                        <div class="small text-muted">
                                            <?php echo truncateText(htmlspecialchars($comm['message']), 100); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php echo htmlspecialchars($comm['employee_name']); ?>
                                    </td>
                                    <td>
                                        <?php
                                        $status_classes = [
                                            'sent' => 'bg-primary',
                                            'delivered' => 'bg-info',
                                            'read' => 'bg-success',
                                            'replied' => 'bg-success',
                                            'failed' => 'bg-danger'
                                        ];
                                        $status_texts = [
                                            'sent' => 'مرسل',
                                            'delivered' => 'تم التسليم',
                                            'read' => 'مقروء',
                                            'replied' => 'تم الرد',
                                            'failed' => 'فشل'
                                        ];
                                        ?>
                                        <span class="badge <?php echo $status_classes[$comm['status']] ?? 'bg-secondary'; ?>">
                                            <?php echo $status_texts[$comm['status']] ?? $comm['status']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-info" 
                                                    onclick="viewCommunication(<?php echo $comm['id']; ?>)" 
                                                    title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <a href="view-client.php?id=<?php echo $comm['client_id']; ?>" 
                                               class="btn btn-outline-primary" title="عرض العميل">
                                                <i class="fas fa-user"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- ترقيم الصفحات -->
                <?php if ($total_pages > 1): ?>
                <div class="card-footer">
                    <nav aria-label="ترقيم الصفحات">
                        <ul class="pagination justify-content-center mb-0">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">السابق</a>
                            </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                            </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">التالي</a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
</div>

<!-- نافذة عرض تفاصيل التواصل -->
<div class="modal fade" id="communicationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل التواصل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="communicationDetails">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
function viewCommunication(commId) {
    // البحث عن التواصل في البيانات المحملة
    const communications = <?php echo json_encode($communications); ?>;
    const comm = communications.find(c => c.id == commId);
    
    if (comm) {
        const typeIcons = {
            'email': '<i class="fas fa-envelope text-primary"></i>',
            'whatsapp': '<i class="fab fa-whatsapp text-success"></i>',
            'phone': '<i class="fas fa-phone text-info"></i>'
        };
        
        const statusTexts = {
            'sent': 'مرسل',
            'delivered': 'تم التسليم',
            'read': 'مقروء',
            'replied': 'تم الرد',
            'failed': 'فشل'
        };
        
        const details = `
            <div class="row">
                <div class="col-md-6">
                    <p><strong>العميل:</strong> ${comm.client_name}</p>
                    <p><strong>المؤسسة:</strong> ${comm.organization}</p>
                    <p><strong>الموظف:</strong> ${comm.employee_name}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>نوع التواصل:</strong> ${typeIcons[comm.communication_type] || ''} ${comm.communication_type}</p>
                    <p><strong>الحالة:</strong> ${statusTexts[comm.status] || comm.status}</p>
                    <p><strong>التاريخ:</strong> ${new Date(comm.created_at).toLocaleString('ar-SA')}</p>
                </div>
            </div>
            ${comm.subject ? `<div class="mb-3"><strong>الموضوع:</strong><br>${comm.subject}</div>` : ''}
            <div class="mb-3">
                <strong>المحتوى:</strong><br>
                <div class="border p-3 bg-light" style="white-space: pre-wrap;">${comm.message}</div>
            </div>
            ${comm.response ? `<div class="mb-3"><strong>الرد:</strong><br><div class="border p-3 bg-light">${comm.response}</div></div>` : ''}
        `;
        
        document.getElementById('communicationDetails').innerHTML = details;
        const modal = new bootstrap.Modal(document.getElementById('communicationModal'));
        modal.show();
    }
}
</script>

<?php include 'includes/footer.php'; ?>
