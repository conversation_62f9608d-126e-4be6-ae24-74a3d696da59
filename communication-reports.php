<?php
/**
 * صفحة تقارير التواصل - نظام التواصل الذكي
 * Communication Reports Page - Smart Communication System
 */

require_once 'includes/functions.php';

// بدء الجلسة والتحقق من تسجيل الدخول
startSecureSession();
if (!isLoggedIn()) {
    redirect('login.php');
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['user_role'];

// معاملات التصفية
$date_from = Config::sanitizeInput($_GET['date_from'] ?? date('Y-m-01'));
$date_to = Config::sanitizeInput($_GET['date_to'] ?? date('Y-m-d'));
$communication_type = Config::sanitizeInput($_GET['communication_type'] ?? '');
$status = Config::sanitizeInput($_GET['status'] ?? '');
$employee_id = Config::sanitizeInput($_GET['employee_id'] ?? '');
$client_id = Config::sanitizeInput($_GET['client_id'] ?? '');
$report_type = Config::sanitizeInput($_GET['report_type'] ?? 'summary');

try {
    $db = new Database();

    // بناء شروط الاستعلام
    $where_conditions = ["cl.created_at BETWEEN ? AND ?"];
    $params = [$date_from . ' 00:00:00', $date_to . ' 23:59:59'];

    // تصفية حسب الموظف (للموظفين العاديين)
    if ($user_role == 'employee') {
        $where_conditions[] = "cl.employee_id = ?";
        $params[] = $user_id;
    } elseif (!empty($employee_id)) {
        $where_conditions[] = "cl.employee_id = ?";
        $params[] = $employee_id;
    }

    // تصفية حسب نوع التواصل
    if (!empty($communication_type)) {
        $where_conditions[] = "cl.communication_type = ?";
        $params[] = $communication_type;
    }

    // تصفية حسب الحالة
    if (!empty($status)) {
        $where_conditions[] = "cl.status = ?";
        $params[] = $status;
    }

    // تصفية حسب العميل
    if (!empty($client_id)) {
        $where_conditions[] = "cl.client_id = ?";
        $params[] = $client_id;
    }

    $where_clause = implode(' AND ', $where_conditions);

    // تقرير ملخص التواصل
    if ($report_type == 'summary') {
        $summary_query = "SELECT
            COUNT(*) as total_communications,
            COUNT(CASE WHEN cl.communication_type = 'email' THEN 1 END) as email_count,
            COUNT(CASE WHEN cl.communication_type = 'whatsapp' THEN 1 END) as whatsapp_count,
            COUNT(CASE WHEN cl.communication_type = 'phone' THEN 1 END) as phone_count,
            COUNT(CASE WHEN cl.communication_type = 'meeting' THEN 1 END) as meeting_count,
            COUNT(CASE WHEN cl.status = 'sent' THEN 1 END) as sent_count,
            COUNT(CASE WHEN cl.status = 'delivered' THEN 1 END) as delivered_count,
            COUNT(CASE WHEN cl.status = 'failed' THEN 1 END) as failed_count,
            COUNT(CASE WHEN cl.status = 'pending' THEN 1 END) as pending_count,
            COUNT(DISTINCT cl.client_id) as unique_clients,
            COUNT(DISTINCT cl.employee_id) as unique_employees
            FROM communication_log cl
            LEFT JOIN clients c ON cl.client_id = c.id
            LEFT JOIN users u ON cl.employee_id = u.id
            WHERE $where_clause";

        $summary_stmt = $db->executeQuery($summary_query, $params);
        $summary_data = $summary_stmt->fetch();

        // تفاصيل التواصل
        $details_query = "SELECT
            cl.*,
            c.name as client_name,
            c.email as client_email,
            u.full_name as employee_name
            FROM communication_log cl
            LEFT JOIN clients c ON cl.client_id = c.id
            LEFT JOIN users u ON cl.employee_id = u.id
            WHERE $where_clause
            ORDER BY cl.created_at DESC
            LIMIT 100";

        $details_stmt = $db->executeQuery($details_query, $params);
        $details_data = $details_stmt->fetchAll();
    }

    // تقرير التواصل اليومي
    elseif ($report_type == 'daily') {
        $daily_query = "SELECT
            DATE(cl.created_at) as communication_date,
            COUNT(*) as total_communications,
            COUNT(CASE WHEN cl.communication_type = 'email' THEN 1 END) as email_count,
            COUNT(CASE WHEN cl.communication_type = 'whatsapp' THEN 1 END) as whatsapp_count,
            COUNT(CASE WHEN cl.communication_type = 'phone' THEN 1 END) as phone_count,
            COUNT(CASE WHEN cl.status = 'sent' THEN 1 END) as sent_count,
            COUNT(CASE WHEN cl.status = 'failed' THEN 1 END) as failed_count,
            COUNT(DISTINCT cl.client_id) as unique_clients
            FROM communication_log cl
            LEFT JOIN clients c ON cl.client_id = c.id
            LEFT JOIN users u ON cl.employee_id = u.id
            WHERE $where_clause
            GROUP BY DATE(cl.created_at)
            ORDER BY communication_date DESC";

        $daily_stmt = $db->executeQuery($daily_query, $params);
        $daily_data = $daily_stmt->fetchAll();
    }

    // تقرير التواصل حسب الموظف
    elseif ($report_type == 'by_employee') {
        $employee_query = "SELECT
            u.full_name as employee_name,
            u.department,
            COUNT(*) as total_communications,
            COUNT(CASE WHEN cl.communication_type = 'email' THEN 1 END) as email_count,
            COUNT(CASE WHEN cl.communication_type = 'whatsapp' THEN 1 END) as whatsapp_count,
            COUNT(CASE WHEN cl.communication_type = 'phone' THEN 1 END) as phone_count,
            COUNT(CASE WHEN cl.status = 'sent' THEN 1 END) as sent_count,
            COUNT(CASE WHEN cl.status = 'failed' THEN 1 END) as failed_count,
            COUNT(DISTINCT cl.client_id) as unique_clients,
            MAX(cl.created_at) as last_communication,
            ROUND(COUNT(*) / DATEDIFF(?, ?), 2) as daily_average
            FROM communication_log cl
            LEFT JOIN clients c ON cl.client_id = c.id
            LEFT JOIN users u ON cl.employee_id = u.id
            WHERE $where_clause
            GROUP BY cl.employee_id
            ORDER BY total_communications DESC";

        $emp_params = array_merge($params, [$date_to, $date_from]);
        $employee_stmt = $db->executeQuery($employee_query, $emp_params);
        $employee_data = $employee_stmt->fetchAll();
    }

    // تقرير التواصل حسب العميل
    elseif ($report_type == 'by_client') {
        $client_query = "SELECT
            c.name as client_name,
            c.email as client_email,
            c.company,
            u.full_name as assigned_employee,
            COUNT(*) as total_communications,
            COUNT(CASE WHEN cl.communication_type = 'email' THEN 1 END) as email_count,
            COUNT(CASE WHEN cl.communication_type = 'whatsapp' THEN 1 END) as whatsapp_count,
            COUNT(CASE WHEN cl.communication_type = 'phone' THEN 1 END) as phone_count,
            COUNT(CASE WHEN cl.status = 'sent' THEN 1 END) as sent_count,
            COUNT(CASE WHEN cl.status = 'failed' THEN 1 END) as failed_count,
            MIN(cl.created_at) as first_communication,
            MAX(cl.created_at) as last_communication
            FROM communication_log cl
            LEFT JOIN clients c ON cl.client_id = c.id
            LEFT JOIN users u ON c.assigned_employee_id = u.id
            WHERE $where_clause
            GROUP BY cl.client_id
            ORDER BY total_communications DESC";

        $client_stmt = $db->executeQuery($client_query, $params);
        $client_data = $client_stmt->fetchAll();
    }

    // قوائم للتصفية
    $employees_query = "SELECT id, full_name FROM users WHERE role IN ('employee', 'supervisor') ORDER BY full_name";
    $employees_stmt = $db->executeQuery($employees_query);
    $employees = $employees_stmt->fetchAll();

    $clients_query = "SELECT id, name FROM clients ORDER BY name LIMIT 100";
    $clients_stmt = $db->executeQuery($clients_query);
    $clients = $clients_stmt->fetchAll();

} catch (Exception $e) {
    Config::logError("Communication reports error: " . $e->getMessage());
    showAlert('حدث خطأ في تحميل التقارير', 'danger');
    $summary_data = [];
    $details_data = [];
    $daily_data = [];
    $employee_data = [];
    $client_data = [];
    $employees = [];
    $clients = [];
}

$page_title = "تقارير التواصل";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">تقارير التواصل</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="exportReport()">
                            <i class="fas fa-download me-1"></i>
                            تصدير
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>
                            طباعة
                        </button>
                    </div>
                </div>
            </div>

            <!-- نموذج التصفية -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-2">
                            <label for="report_type" class="form-label">نوع التقرير</label>
                            <select class="form-select" id="report_type" name="report_type">
                                <option value="summary" <?php echo $report_type == 'summary' ? 'selected' : ''; ?>>ملخص التواصل</option>
                                <option value="daily" <?php echo $report_type == 'daily' ? 'selected' : ''; ?>>تقرير يومي</option>
                                <option value="by_employee" <?php echo $report_type == 'by_employee' ? 'selected' : ''; ?>>حسب الموظف</option>
                                <option value="by_client" <?php echo $report_type == 'by_client' ? 'selected' : ''; ?>>حسب العميل</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from"
                                   value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to"
                                   value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="communication_type" class="form-label">نوع التواصل</label>
                            <select class="form-select" id="communication_type" name="communication_type">
                                <option value="">جميع الأنواع</option>
                                <option value="email" <?php echo $communication_type == 'email' ? 'selected' : ''; ?>>بريد إلكتروني</option>
                                <option value="whatsapp" <?php echo $communication_type == 'whatsapp' ? 'selected' : ''; ?>>واتساب</option>
                                <option value="phone" <?php echo $communication_type == 'phone' ? 'selected' : ''; ?>>هاتف</option>
                                <option value="meeting" <?php echo $communication_type == 'meeting' ? 'selected' : ''; ?>>اجتماع</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="sent" <?php echo $status == 'sent' ? 'selected' : ''; ?>>مرسل</option>
                                <option value="delivered" <?php echo $status == 'delivered' ? 'selected' : ''; ?>>تم التسليم</option>
                                <option value="failed" <?php echo $status == 'failed' ? 'selected' : ''; ?>>فشل</option>
                                <option value="pending" <?php echo $status == 'pending' ? 'selected' : ''; ?>>معلق</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>
                                    عرض التقرير
                                </button>
                            </div>
                        </div>

                        <?php if ($user_role != 'employee'): ?>
                        <div class="col-md-3">
                            <label for="employee_id" class="form-label">الموظف</label>
                            <select class="form-select" id="employee_id" name="employee_id">
                                <option value="">جميع الموظفين</option>
                                <?php foreach ($employees as $employee): ?>
                                <option value="<?php echo $employee['id']; ?>"
                                        <?php echo $employee_id == $employee['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($employee['full_name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <?php endif; ?>

                        <div class="col-md-3">
                            <label for="client_id" class="form-label">العميل</label>
                            <select class="form-select" id="client_id" name="client_id">
                                <option value="">جميع العملاء</option>
                                <?php foreach ($clients as $client): ?>
                                <option value="<?php echo $client['id']; ?>"
                                        <?php echo $client_id == $client['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($client['name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </form>
                </div>
            </div>

            <?php if ($report_type == 'summary'): ?>
            <!-- ملخص التواصل -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($summary_data['total_communications'] ?? 0); ?></div>
                            <div>إجمالي التواصل</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($summary_data['email_count'] ?? 0); ?></div>
                            <div>بريد إلكتروني</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($summary_data['whatsapp_count'] ?? 0); ?></div>
                            <div>واتساب</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($summary_data['phone_count'] ?? 0); ?></div>
                            <div>مكالمات</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($summary_data['sent_count'] ?? 0); ?></div>
                            <div>مرسل بنجاح</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($summary_data['failed_count'] ?? 0); ?></div>
                            <div>فشل الإرسال</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($summary_data['unique_clients'] ?? 0); ?></div>
                            <div>عملاء فريدين</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-dark text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($summary_data['unique_employees'] ?? 0); ?></div>
                            <div>موظفين نشطين</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول تفاصيل التواصل -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">آخر 100 عملية تواصل</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>العميل</th>
                                    <th>الموظف</th>
                                    <th>النوع</th>
                                    <th>الموضوع</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($details_data)): ?>
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد بيانات تواصل للفترة المحددة</p>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($details_data as $detail): ?>
                                <tr>
                                    <td><?php echo formatDateArabic($detail['created_at']); ?></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($detail['client_name'] ?? 'غير محدد'); ?></strong>
                                        <?php if ($detail['client_email']): ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($detail['client_email']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($detail['employee_name'] ?? 'غير محدد'); ?></td>
                                    <td>
                                        <?php
                                        $type_badges = [
                                            'email' => '<span class="badge bg-primary">بريد إلكتروني</span>',
                                            'whatsapp' => '<span class="badge bg-success">واتساب</span>',
                                            'phone' => '<span class="badge bg-warning">هاتف</span>',
                                            'meeting' => '<span class="badge bg-info">اجتماع</span>'
                                        ];
                                        echo $type_badges[$detail['communication_type']] ?? $detail['communication_type'];
                                        ?>
                                    </td>
                                    <td><?php echo htmlspecialchars(truncateText($detail['subject'] ?? '', 50)); ?></td>
                                    <td>
                                        <?php
                                        $status_badges = [
                                            'sent' => '<span class="badge bg-success">مرسل</span>',
                                            'delivered' => '<span class="badge bg-info">تم التسليم</span>',
                                            'failed' => '<span class="badge bg-danger">فشل</span>',
                                            'pending' => '<span class="badge bg-warning">معلق</span>'
                                        ];
                                        echo $status_badges[$detail['status']] ?? $detail['status'];
                                        ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <?php elseif ($report_type == 'daily'): ?>
            <!-- التقرير اليومي -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">التقرير اليومي للتواصل</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>إجمالي التواصل</th>
                                    <th>بريد إلكتروني</th>
                                    <th>واتساب</th>
                                    <th>هاتف</th>
                                    <th>نجح</th>
                                    <th>فشل</th>
                                    <th>عملاء فريدين</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($daily_data)): ?>
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <i class="fas fa-calendar fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد بيانات للفترة المحددة</p>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($daily_data as $daily): ?>
                                <tr>
                                    <td><strong><?php echo formatDateArabic($daily['communication_date']); ?></strong></td>
                                    <td><span class="badge bg-primary fs-6"><?php echo number_format($daily['total_communications']); ?></span></td>
                                    <td><span class="badge bg-info"><?php echo number_format($daily['email_count']); ?></span></td>
                                    <td><span class="badge bg-success"><?php echo number_format($daily['whatsapp_count']); ?></span></td>
                                    <td><span class="badge bg-warning"><?php echo number_format($daily['phone_count']); ?></span></td>
                                    <td><span class="badge bg-success"><?php echo number_format($daily['sent_count']); ?></span></td>
                                    <td><span class="badge bg-danger"><?php echo number_format($daily['failed_count']); ?></span></td>
                                    <td><span class="badge bg-secondary"><?php echo number_format($daily['unique_clients']); ?></span></td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <?php elseif ($report_type == 'by_employee'): ?>
            <!-- التقرير حسب الموظف -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">تقرير التواصل حسب الموظف</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>الموظف</th>
                                    <th>القسم</th>
                                    <th>إجمالي التواصل</th>
                                    <th>بريد إلكتروني</th>
                                    <th>واتساب</th>
                                    <th>هاتف</th>
                                    <th>نجح</th>
                                    <th>فشل</th>
                                    <th>عملاء فريدين</th>
                                    <th>المعدل اليومي</th>
                                    <th>آخر تواصل</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($employee_data)): ?>
                                <tr>
                                    <td colspan="11" class="text-center py-4">
                                        <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد بيانات موظفين</p>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($employee_data as $emp): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($emp['employee_name']); ?></strong></td>
                                    <td><?php echo htmlspecialchars($emp['department'] ?? 'غير محدد'); ?></td>
                                    <td><span class="badge bg-primary fs-6"><?php echo number_format($emp['total_communications']); ?></span></td>
                                    <td><span class="badge bg-info"><?php echo number_format($emp['email_count']); ?></span></td>
                                    <td><span class="badge bg-success"><?php echo number_format($emp['whatsapp_count']); ?></span></td>
                                    <td><span class="badge bg-warning"><?php echo number_format($emp['phone_count']); ?></span></td>
                                    <td><span class="badge bg-success"><?php echo number_format($emp['sent_count']); ?></span></td>
                                    <td><span class="badge bg-danger"><?php echo number_format($emp['failed_count']); ?></span></td>
                                    <td><span class="badge bg-secondary"><?php echo number_format($emp['unique_clients']); ?></span></td>
                                    <td><span class="badge bg-dark"><?php echo $emp['daily_average']; ?></span></td>
                                    <td><?php echo $emp['last_communication'] ? formatDateArabic($emp['last_communication']) : 'لا يوجد'; ?></td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <?php elseif ($report_type == 'by_client'): ?>
            <!-- التقرير حسب العميل -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">تقرير التواصل حسب العميل</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>العميل</th>
                                    <th>الشركة</th>
                                    <th>الموظف المسؤول</th>
                                    <th>إجمالي التواصل</th>
                                    <th>بريد إلكتروني</th>
                                    <th>واتساب</th>
                                    <th>هاتف</th>
                                    <th>نجح</th>
                                    <th>فشل</th>
                                    <th>أول تواصل</th>
                                    <th>آخر تواصل</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($client_data)): ?>
                                <tr>
                                    <td colspan="11" class="text-center py-4">
                                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد بيانات عملاء</p>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($client_data as $client): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($client['client_name']); ?></strong>
                                        <?php if ($client['client_email']): ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($client['client_email']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($client['company'] ?? 'غير محدد'); ?></td>
                                    <td><?php echo htmlspecialchars($client['assigned_employee'] ?? 'غير معين'); ?></td>
                                    <td><span class="badge bg-primary fs-6"><?php echo number_format($client['total_communications']); ?></span></td>
                                    <td><span class="badge bg-info"><?php echo number_format($client['email_count']); ?></span></td>
                                    <td><span class="badge bg-success"><?php echo number_format($client['whatsapp_count']); ?></span></td>
                                    <td><span class="badge bg-warning"><?php echo number_format($client['phone_count']); ?></span></td>
                                    <td><span class="badge bg-success"><?php echo number_format($client['sent_count']); ?></span></td>
                                    <td><span class="badge bg-danger"><?php echo number_format($client['failed_count']); ?></span></td>
                                    <td><?php echo formatDateArabic($client['first_communication']); ?></td>
                                    <td><?php echo formatDateArabic($client['last_communication']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </main>
    </div>
</div>

<script>
function exportReport() {
    // تصدير التقرير كـ CSV
    const table = document.querySelector('.table');
    if (!table) return;

    let csv = '';
    const rows = table.querySelectorAll('tr');

    for (let i = 0; i < rows.length; i++) {
        const cols = rows[i].querySelectorAll('td, th');
        const rowData = [];

        for (let j = 0; j < cols.length; j++) {
            let cellData = cols[j].textContent.trim();
            cellData = cellData.replace(/"/g, '""'); // escape quotes
            rowData.push('"' + cellData + '"');
        }

        csv += rowData.join(',') + '\n';
    }

    // تحميل الملف
    const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'communication_report_<?php echo date('Y-m-d'); ?>.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// تحديث التقرير عند تغيير النوع
document.getElementById('report_type').addEventListener('change', function() {
    this.form.submit();
});
</script>

<?php include 'includes/footer.php'; ?>