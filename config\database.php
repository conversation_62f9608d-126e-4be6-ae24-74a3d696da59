<?php
/**
 * إعدادات قاعدة البيانات - نظام التواصل الذكي
 * Database Configuration - Smart Communication System
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'smart_communication';
    private $username = 'root';
    private $password = '';
    private $charset = 'utf8mb4';
    private $conn;
    
    /**
     * الاتصال بقاعدة البيانات
     * Connect to database
     */
    public function connect() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];
            
            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch(PDOException $e) {
            error_log("Database Connection Error: " . $e->getMessage());
            throw new Exception("فشل في الاتصال بقاعدة البيانات");
        }
        
        return $this->conn;
    }
    
    /**
     * إغلاق الاتصال
     * Close connection
     */
    public function disconnect() {
        $this->conn = null;
    }
    
    /**
     * تنفيذ استعلام محضر
     * Execute prepared statement
     */
    public function executeQuery($query, $params = []) {
        try {
            if (!$this->conn) {
                $this->connect();
            }
            
            $stmt = $this->conn->prepare($query);
            $stmt->execute($params);
            
            return $stmt;
            
        } catch(PDOException $e) {
            error_log("Query Execution Error: " . $e->getMessage());
            throw new Exception("خطأ في تنفيذ الاستعلام");
        }
    }
    
    /**
     * الحصول على آخر معرف مدرج
     * Get last inserted ID
     */
    public function getLastInsertId() {
        return $this->conn->lastInsertId();
    }
    
    /**
     * بدء معاملة
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->conn->beginTransaction();
    }
    
    /**
     * تأكيد المعاملة
     * Commit transaction
     */
    public function commit() {
        return $this->conn->commit();
    }
    
    /**
     * إلغاء المعاملة
     * Rollback transaction
     */
    public function rollback() {
        return $this->conn->rollback();
    }
}

/**
 * إعدادات النظام العامة
 * General System Configuration
 */
class Config {
    // إعدادات الأمان
    const ENCRYPTION_KEY = 'your-secret-encryption-key-here-change-this';
    const SESSION_TIMEOUT = 3600; // ساعة واحدة
    const MAX_LOGIN_ATTEMPTS = 5;
    const LOCKOUT_TIME = 900; // 15 دقيقة
    
    // إعدادات البريد الإلكتروني
    const SMTP_HOST = 'smtp.gmail.com';
    const SMTP_PORT = 587;
    const SMTP_USERNAME = '';
    const SMTP_PASSWORD = '';
    const SMTP_ENCRYPTION = 'tls';
    
    // إعدادات واتساب
    const WHATSAPP_API_URL = '';
    const WHATSAPP_API_TOKEN = '';
    
    // إعدادات عامة
    const SITE_NAME = 'نظام التواصل الذكي';
    const COMPANY_NAME = 'شركتك';
    const TIMEZONE = 'Asia/Amman';
    const LANGUAGE = 'ar';
    
    // مسارات الملفات
    const UPLOAD_PATH = 'uploads/';
    const LOG_PATH = 'logs/';
    const BACKUP_PATH = 'backups/';
    
    /**
     * تشفير البيانات
     * Encrypt data
     */
    public static function encrypt($data) {
        $key = hash('sha256', self::ENCRYPTION_KEY);
        $iv = openssl_random_pseudo_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        return base64_encode($encrypted . '::' . $iv);
    }
    
    /**
     * فك تشفير البيانات
     * Decrypt data
     */
    public static function decrypt($data) {
        $key = hash('sha256', self::ENCRYPTION_KEY);
        list($encrypted_data, $iv) = explode('::', base64_decode($data), 2);
        return openssl_decrypt($encrypted_data, 'AES-256-CBC', $key, 0, $iv);
    }
    
    /**
     * تسجيل الأخطاء
     * Log errors
     */
    public static function logError($message, $file = 'error.log') {
        $timestamp = date('Y-m-d H:i:s');
        $log_message = "[$timestamp] $message" . PHP_EOL;
        
        if (!is_dir(self::LOG_PATH)) {
            mkdir(self::LOG_PATH, 0755, true);
        }
        
        file_put_contents(self::LOG_PATH . $file, $log_message, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * تنظيف البيانات المدخلة
     * Sanitize input data
     */
    public static function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([self::class, 'sanitizeInput'], $data);
        }
        
        return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
    }
}

// تعيين المنطقة الزمنية
date_default_timezone_set(Config::TIMEZONE);

// تعيين الترميز
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');
?>
