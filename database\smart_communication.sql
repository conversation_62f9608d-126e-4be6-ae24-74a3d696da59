-- قاعدة بيانات نظام التواصل الذكي
-- Smart Communication System Database

CREATE DATABASE IF NOT EXISTS smart_communication CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE smart_communication;

-- جدول المستخدمين والموظفين
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'employee', 'supervisor') DEFAULT 'employee',
    phone VARCHAR(20),
    department VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول العملاء
CREATE TABLE clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    organization VARCHAR(150) NOT NULL,
    sector ENUM('educational', 'government', 'private') NOT NULL,
    email VARCHAR(100),
    whatsapp_number VARCHAR(20),
    phone VARCHAR(20),
    address TEXT,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    status ENUM('active', 'inactive', 'potential') DEFAULT 'active',
    assigned_employee_id INT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (assigned_employee_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_sector (sector),
    INDEX idx_organization (organization),
    INDEX idx_assigned_employee (assigned_employee_id)
);

-- جدول سجل التواصل
CREATE TABLE communication_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    employee_id INT NOT NULL,
    communication_type ENUM('email', 'whatsapp', 'phone', 'meeting') NOT NULL,
    subject VARCHAR(200),
    message TEXT,
    status ENUM('sent', 'delivered', 'read', 'replied', 'failed') DEFAULT 'sent',
    response TEXT,
    scheduled_at TIMESTAMP NULL,
    sent_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (employee_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_client_id (client_id),
    INDEX idx_employee_id (employee_id),
    INDEX idx_communication_type (communication_type),
    INDEX idx_created_at (created_at)
);

-- جدول الإشعارات والتذكيرات
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    client_id INT,
    type ENUM('follow_up', 'reminder', 'system', 'alert') NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    scheduled_for TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_read (is_read),
    INDEX idx_scheduled_for (scheduled_for)
);

-- جدول إعدادات النظام
CREATE TABLE system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول سجل دخول وخروج الموظفين
CREATE TABLE user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    logout_time TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_session_token (session_token),
    INDEX idx_is_active (is_active)
);

-- جدول قوالب الرسائل
CREATE TABLE message_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type ENUM('email', 'whatsapp') NOT NULL,
    subject VARCHAR(200),
    content TEXT NOT NULL,
    variables JSON,
    created_by INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_type (type),
    INDEX idx_created_by (created_by)
);

-- إدراج بيانات أولية
-- إنشاء مستخدم مدير افتراضي
INSERT INTO users (username, email, password, full_name, role, is_active) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin', TRUE);

-- إعدادات النظام الافتراضية
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('smtp_host', 'smtp.gmail.com', 'خادم البريد الإلكتروني'),
('smtp_port', '587', 'منفذ البريد الإلكتروني'),
('smtp_username', '', 'اسم المستخدم للبريد الإلكتروني'),
('smtp_password', '', 'كلمة مرور البريد الإلكتروني'),
('whatsapp_api_url', '', 'رابط واجهة واتساب API'),
('whatsapp_api_token', '', 'رمز واجهة واتساب API'),
('system_name', 'نظام التواصل الذكي', 'اسم النظام'),
('company_name', 'شركتك', 'اسم الشركة'),
('follow_up_days', '7', 'عدد الأيام للتذكير بالمتابعة'),
('timezone', 'Asia/Amman', 'المنطقة الزمنية');

-- قوالب رسائل افتراضية
INSERT INTO message_templates (name, type, subject, content, created_by) VALUES
('ترحيب بالعميل الجديد', 'email', 'مرحباً بك في {{company_name}}', 
'عزيزي/عزيزتي {{client_name}},\n\nنرحب بك في {{company_name}}. نحن سعداء لخدمتك وتقديم أفضل الحلول لمؤسستك {{organization}}.\n\nسنتواصل معك قريباً لمناقشة احتياجاتك.\n\nمع أطيب التحيات,\n{{employee_name}}', 1),
('متابعة العميل', 'whatsapp', '', 
'مرحباً {{client_name}}\nنود متابعة وضع مؤسستك {{organization}} والاطمئنان على احتياجاتكم.\nنحن في خدمتكم دائماً.\n{{employee_name}} - {{company_name}}', 1);
