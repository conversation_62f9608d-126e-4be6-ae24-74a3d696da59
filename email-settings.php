<?php
/**
 * إعدادات البريد الإلكتروني - نظام التواصل الذكي
 * Email Settings - Smart Communication System
 */

require_once 'includes/functions.php';
require_once 'includes/SimpleEmailService.php';

// بدء الجلسة والتحقق من تسجيل الدخول
startSecureSession();
if (!isLoggedIn()) {
    redirect('login.php');
}

// التحقق من الصلاحيات (مدير فقط)
if (!hasPermission('admin')) {
    showAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    redirect('index.php');
}

$user_id = $_SESSION['user_id'];

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrf_token)) {
        showAlert('خطأ في التحقق من الأمان', 'danger');
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action == 'save_settings') {
            try {
                // حفظ إعدادات SMTP
                $smtp_enabled = isset($_POST['smtp_enabled']) ? 'true' : 'false';
                $smtp_host = Config::sanitizeInput($_POST['smtp_host'] ?? '');
                $smtp_port = (int)($_POST['smtp_port'] ?? 587);
                $smtp_username = Config::sanitizeInput($_POST['smtp_username'] ?? '');
                $smtp_password = $_POST['smtp_password'] ?? '';
                $smtp_secure = Config::sanitizeInput($_POST['smtp_secure'] ?? 'tls');
                
                // حفظ الإعدادات
                updateSystemSetting('smtp_enabled', $smtp_enabled);
                updateSystemSetting('smtp_host', $smtp_host);
                updateSystemSetting('smtp_port', $smtp_port);
                updateSystemSetting('smtp_username', $smtp_username);
                if (!empty($smtp_password)) {
                    updateSystemSetting('smtp_password', $smtp_password);
                }
                updateSystemSetting('smtp_secure', $smtp_secure);
                
                logUserActivity($user_id, 'update_email_settings', 'تحديث إعدادات البريد الإلكتروني');
                showAlert('تم حفظ إعدادات البريد الإلكتروني بنجاح', 'success');
                
            } catch (Exception $e) {
                Config::logError("Email settings save error: " . $e->getMessage());
                showAlert('حدث خطأ في حفظ الإعدادات', 'danger');
            }
        }
        
        elseif ($action == 'test_connection') {
            try {
                $email_service = new SimpleEmailService();
                $test_result = $email_service->testEmailSettings();
                
                if ($test_result['success']) {
                    showAlert($test_result['message'], 'success');
                } else {
                    showAlert($test_result['message'], 'warning');
                }
                
            } catch (Exception $e) {
                showAlert('خطأ في اختبار الاتصال: ' . $e->getMessage(), 'danger');
            }
        }
        
        elseif ($action == 'send_test_email') {
            $test_email = Config::sanitizeInput($_POST['test_email'] ?? '');
            
            if (empty($test_email) || !filter_var($test_email, FILTER_VALIDATE_EMAIL)) {
                showAlert('يرجى إدخال بريد إلكتروني صحيح للاختبار', 'warning');
            } else {
                try {
                    $email_service = new SimpleEmailService();
                    $result = $email_service->sendTestEmail($test_email);
                    
                    if ($result['success']) {
                        showAlert('تم إرسال رسالة الاختبار بنجاح', 'success');
                    } else {
                        showAlert($result['message'], 'danger');
                    }
                    
                } catch (Exception $e) {
                    showAlert('خطأ في إرسال رسالة الاختبار: ' . $e->getMessage(), 'danger');
                }
            }
        }
    }
    
    redirect('email-settings.php');
}

// تحميل الإعدادات الحالية
$smtp_enabled = getSystemSetting('smtp_enabled', 'false') === 'true';
$smtp_host = getSystemSetting('smtp_host', 'smtp.gmail.com');
$smtp_port = getSystemSetting('smtp_port', '587');
$smtp_username = getSystemSetting('smtp_username', '');
$smtp_secure = getSystemSetting('smtp_secure', 'tls');

$page_title = "إعدادات البريد الإلكتروني";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إعدادات البريد الإلكتروني</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="testConnection()">
                            <i class="fas fa-plug me-1"></i>
                            اختبار الاتصال
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="showTestEmailModal()">
                            <i class="fas fa-paper-plane me-1"></i>
                            إرسال رسالة اختبار
                        </button>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <!-- إعدادات SMTP -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">إعدادات SMTP</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" id="emailSettingsForm">
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                <input type="hidden" name="action" value="save_settings">
                                
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="smtp_enabled" name="smtp_enabled" 
                                               <?php echo $smtp_enabled ? 'checked' : ''; ?> onchange="toggleSMTPFields()">
                                        <label class="form-check-label" for="smtp_enabled">
                                            تفعيل SMTP
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">
                                        إذا لم يتم تفعيل SMTP، سيعمل النظام في وضع المحاكاة (حفظ الرسائل في ملف السجل)
                                    </small>
                                </div>
                                
                                <div id="smtpFields" style="<?php echo $smtp_enabled ? '' : 'display: none;'; ?>">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="mb-3">
                                                <label for="smtp_host" class="form-label">خادم SMTP</label>
                                                <input type="text" class="form-control" id="smtp_host" name="smtp_host" 
                                                       value="<?php echo htmlspecialchars($smtp_host); ?>" 
                                                       placeholder="smtp.gmail.com">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label for="smtp_port" class="form-label">المنفذ</label>
                                                <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                                                       value="<?php echo htmlspecialchars($smtp_port); ?>" 
                                                       placeholder="587">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="smtp_username" class="form-label">اسم المستخدم (البريد الإلكتروني)</label>
                                        <input type="email" class="form-control" id="smtp_username" name="smtp_username" 
                                               value="<?php echo htmlspecialchars($smtp_username); ?>" 
                                               placeholder="<EMAIL>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="smtp_password" class="form-label">كلمة المرور</label>
                                        <input type="password" class="form-control" id="smtp_password" name="smtp_password" 
                                               placeholder="اتركها فارغة إذا لم تريد تغييرها">
                                        <small class="form-text text-muted">
                                            لـ Gmail: استخدم App Password بدلاً من كلمة المرور العادية
                                        </small>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="smtp_secure" class="form-label">نوع التشفير</label>
                                        <select class="form-select" id="smtp_secure" name="smtp_secure">
                                            <option value="tls" <?php echo $smtp_secure == 'tls' ? 'selected' : ''; ?>>TLS (587)</option>
                                            <option value="ssl" <?php echo $smtp_secure == 'ssl' ? 'selected' : ''; ?>>SSL (465)</option>
                                            <option value="none" <?php echo $smtp_secure == 'none' ? 'selected' : ''; ?>>بدون تشفير (25)</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ الإعدادات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <!-- معلومات مساعدة -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">إعدادات شائعة</h5>
                        </div>
                        <div class="card-body">
                            <h6>Gmail:</h6>
                            <ul class="list-unstyled small">
                                <li><strong>الخادم:</strong> smtp.gmail.com</li>
                                <li><strong>المنفذ:</strong> 587</li>
                                <li><strong>التشفير:</strong> TLS</li>
                                <li><strong>ملاحظة:</strong> استخدم App Password</li>
                            </ul>
                            
                            <h6>Outlook/Hotmail:</h6>
                            <ul class="list-unstyled small">
                                <li><strong>الخادم:</strong> smtp-mail.outlook.com</li>
                                <li><strong>المنفذ:</strong> 587</li>
                                <li><strong>التشفير:</strong> TLS</li>
                            </ul>
                            
                            <h6>Yahoo:</h6>
                            <ul class="list-unstyled small">
                                <li><strong>الخادم:</strong> smtp.mail.yahoo.com</li>
                                <li><strong>المنفذ:</strong> 587</li>
                                <li><strong>التشفير:</strong> TLS</li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- حالة النظام -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0">حالة النظام</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-circle <?php echo $smtp_enabled ? 'text-success' : 'text-warning'; ?> me-2"></i>
                                <span><?php echo $smtp_enabled ? 'SMTP مفعل' : 'وضع المحاكاة'; ?></span>
                            </div>
                            
                            <?php if ($smtp_enabled): ?>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-server text-info me-2"></i>
                                <span><?php echo htmlspecialchars($smtp_host . ':' . $smtp_port); ?></span>
                            </div>
                            
                            <div class="d-flex align-items-center">
                                <i class="fas fa-user text-secondary me-2"></i>
                                <span><?php echo htmlspecialchars($smtp_username); ?></span>
                            </div>
                            <?php else: ?>
                            <small class="text-muted">
                                في وضع المحاكاة، سيتم حفظ الرسائل في ملف السجل بدلاً من الإرسال الفعلي.
                            </small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- نموذج إرسال رسالة اختبار -->
<div class="modal fade" id="testEmailModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إرسال رسالة اختبار</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="send_test_email">
                    
                    <div class="mb-3">
                        <label for="test_email" class="form-label">البريد الإلكتروني للاختبار</label>
                        <input type="email" class="form-control" id="test_email" name="test_email" 
                               placeholder="<EMAIL>" required>
                        <small class="form-text text-muted">
                            سيتم إرسال رسالة اختبار إلى هذا البريد الإلكتروني
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إرسال</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleSMTPFields() {
    const smtpEnabled = document.getElementById('smtp_enabled').checked;
    const smtpFields = document.getElementById('smtpFields');
    
    if (smtpEnabled) {
        smtpFields.style.display = 'block';
    } else {
        smtpFields.style.display = 'none';
    }
}

function testConnection() {
    const form = document.createElement('form');
    form.method = 'POST';
    form.style.display = 'none';
    
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = 'csrf_token';
    csrfToken.value = '<?php echo generateCSRFToken(); ?>';
    
    const action = document.createElement('input');
    action.type = 'hidden';
    action.name = 'action';
    action.value = 'test_connection';
    
    form.appendChild(csrfToken);
    form.appendChild(action);
    document.body.appendChild(form);
    form.submit();
}

function showTestEmailModal() {
    const modal = new bootstrap.Modal(document.getElementById('testEmailModal'));
    modal.show();
}

// تحديث المنفذ عند تغيير نوع التشفير
document.getElementById('smtp_secure').addEventListener('change', function() {
    const portField = document.getElementById('smtp_port');
    const secureType = this.value;
    
    if (secureType === 'tls') {
        portField.value = '587';
    } else if (secureType === 'ssl') {
        portField.value = '465';
    } else if (secureType === 'none') {
        portField.value = '25';
    }
});
</script>

<?php include 'includes/footer.php'; ?>
