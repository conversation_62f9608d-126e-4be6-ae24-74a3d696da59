<?php
/**
 * سجل محاكاة البريد الإلكتروني - نظام التواصل الذكي
 * Email Simulation Log - Smart Communication System
 */

require_once 'includes/functions.php';

// بدء الجلسة والتحقق من تسجيل الدخول
startSecureSession();
if (!isLoggedIn()) {
    redirect('login.php');
}

$user_id = $_SESSION['user_id'];

// قراءة سجل المحاكاة
$simulation_log = [];
$log_file = 'logs/email_simulation_details.json';

if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $simulation_log = json_decode($log_content, true) ?: [];
    // ترتيب عكسي لإظهار الأحدث أولاً
    $simulation_log = array_reverse($simulation_log);
}

// معاملات التصفية
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 20;
$total_emails = count($simulation_log);
$total_pages = ceil($total_emails / $per_page);
$offset = ($page - 1) * $per_page;
$current_page_emails = array_slice($simulation_log, $offset, $per_page);

$page_title = "سجل محاكاة البريد الإلكتروني";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">سجل محاكاة البريد الإلكتروني</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="email-settings.php" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-cog me-1"></i>
                            إعدادات البريد
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshPage()">
                            <i class="fas fa-sync me-1"></i>
                            تحديث
                        </button>
                    </div>
                </div>
            </div>

            <!-- معلومات وضع المحاكاة -->
            <div class="alert alert-info mb-4">
                <div class="d-flex align-items-center">
                    <i class="fas fa-info-circle fa-2x me-3"></i>
                    <div>
                        <h5 class="alert-heading mb-1">وضع محاكاة البريد الإلكتروني</h5>
                        <p class="mb-0">
                            النظام يعمل حالياً في وضع المحاكاة. جميع الرسائل يتم حفظها في هذا السجل بدلاً من الإرسال الفعلي.
                            لتفعيل الإرسال الحقيقي، اذهب إلى <a href="email-settings.php" class="alert-link">إعدادات البريد</a> وأعد إعداد SMTP.
                        </p>
                    </div>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($total_emails); ?></div>
                            <div>إجمالي الرسائل المحاكاة</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0">
                                <?php 
                                $today_count = 0;
                                $today = date('Y-m-d');
                                foreach ($simulation_log as $email) {
                                    if (strpos($email['timestamp'], $today) === 0) {
                                        $today_count++;
                                    }
                                }
                                echo number_format($today_count);
                                ?>
                            </div>
                            <div>رسائل اليوم</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0">
                                <?php 
                                $unique_recipients = [];
                                foreach ($simulation_log as $email) {
                                    $unique_recipients[$email['to_email']] = true;
                                }
                                echo number_format(count($unique_recipients));
                                ?>
                            </div>
                            <div>مستقبلين فريدين</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0">
                                <?php 
                                if (!empty($simulation_log)) {
                                    $latest = $simulation_log[0]['timestamp'];
                                    $time_diff = time() - strtotime($latest);
                                    if ($time_diff < 3600) {
                                        echo round($time_diff / 60) . ' دقيقة';
                                    } elseif ($time_diff < 86400) {
                                        echo round($time_diff / 3600) . ' ساعة';
                                    } else {
                                        echo round($time_diff / 86400) . ' يوم';
                                    }
                                } else {
                                    echo 'لا يوجد';
                                }
                                ?>
                            </div>
                            <div>آخر رسالة</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول الرسائل المحاكاة -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        سجل الرسائل المحاكاة (<?php echo number_format($total_emails); ?> رسالة)
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($current_page_emails)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد رسائل محاكاة بعد</p>
                        <a href="send-email.php" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-1"></i>
                            إرسال رسالة تجريبية
                        </a>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>التاريخ والوقت</th>
                                    <th>المستقبل</th>
                                    <th>الموضوع</th>
                                    <th>معاينة المحتوى</th>
                                    <th>حجم الرسالة</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($current_page_emails as $email): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo date('Y-m-d', strtotime($email['timestamp'])); ?></strong>
                                        <br>
                                        <small class="text-muted"><?php echo date('H:i:s', strtotime($email['timestamp'])); ?></small>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($email['to_name']); ?></strong>
                                        <br>
                                        <small class="text-muted"><?php echo htmlspecialchars($email['to_email']); ?></small>
                                    </td>
                                    <td>
                                        <span class="fw-bold"><?php echo htmlspecialchars($email['subject']); ?></span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo htmlspecialchars($email['body_preview'] ?? 'لا توجد معاينة'); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <?php echo number_format($email['body_length']); ?> حرف
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>
                                            محاكاة ناجحة
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
                
                <!-- ترقيم الصفحات -->
                <?php if ($total_pages > 1): ?>
                <div class="card-footer">
                    <nav aria-label="ترقيم الصفحات">
                        <ul class="pagination justify-content-center mb-0">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>">السابق</a>
                            </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                            </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>">التالي</a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
                <?php endif; ?>
            </div>

            <!-- معلومات إضافية -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">معلومات مفيدة</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li><i class="fas fa-info-circle text-info me-2"></i>جميع الرسائل محفوظة في ملف السجل</li>
                                <li><i class="fas fa-shield-alt text-success me-2"></i>لا يتم إرسال رسائل حقيقية</li>
                                <li><i class="fas fa-cog text-warning me-2"></i>يمكن تفعيل SMTP من الإعدادات</li>
                                <li><i class="fas fa-history text-secondary me-2"></i>السجل يحتفظ بآخر 100 رسالة</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">ملفات السجل</h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li>
                                    <i class="fas fa-file-alt text-primary me-2"></i>
                                    <strong>السجل النصي:</strong> logs/email_simulation.log
                                </li>
                                <li>
                                    <i class="fas fa-file-code text-info me-2"></i>
                                    <strong>التفاصيل JSON:</strong> logs/email_simulation_details.json
                                </li>
                                <li>
                                    <i class="fas fa-database text-success me-2"></i>
                                    <strong>قاعدة البيانات:</strong> communication_log
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
function refreshPage() {
    location.reload();
}

// تحديث تلقائي كل 30 ثانية
setInterval(function() {
    if (!document.hidden) {
        // تحديث العدادات فقط بدون إعادة تحميل الصفحة
        fetch(window.location.href)
            .then(response => response.text())
            .then(html => {
                // يمكن إضافة تحديث جزئي هنا إذا لزم الأمر
            })
            .catch(error => console.log('تحديث تلقائي فشل:', error));
    }
}, 30000);
</script>

<?php include 'includes/footer.php'; ?>
