<?php
/**
 * صفحة تقارير الموظفين - نظام التواصل الذكي
 * Employee Reports Page - Smart Communication System
 */

require_once 'includes/functions.php';

// بدء الجلسة والتحقق من تسجيل الدخول
startSecureSession();
if (!isLoggedIn()) {
    redirect('login.php');
}

// التحقق من الصلاحيات (مشرف أو مدير)
if (!hasPermission('supervisor')) {
    showAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    redirect('index.php');
}

$user_id = $_SESSION['user_id'];

// معاملات التصفية
$date_from = Config::sanitizeInput($_GET['date_from'] ?? date('Y-m-01'));
$date_to = Config::sanitizeInput($_GET['date_to'] ?? date('Y-m-d'));
$department = Config::sanitizeInput($_GET['department'] ?? '');
$role = Config::sanitizeInput($_GET['role'] ?? '');
$report_type = Config::sanitizeInput($_GET['report_type'] ?? 'performance');

try {
    $db = new Database();

    // بناء شروط الاستعلام
    $where_conditions = ["u.role IN ('employee', 'supervisor', 'admin')"];
    $params = [];

    // تصفية حسب القسم
    if (!empty($department)) {
        $where_conditions[] = "u.department = ?";
        $params[] = $department;
    }

    // تصفية حسب الصلاحية
    if (!empty($role)) {
        $where_conditions[] = "u.role = ?";
        $params[] = $role;
    }

    $where_clause = implode(' AND ', $where_conditions);

    // تقرير أداء الموظفين
    if ($report_type == 'performance') {
        $performance_query = "SELECT
            u.id,
            u.full_name,
            u.email,
            u.department,
            u.role,
            u.is_active,
            u.last_login,
            u.created_at,
            COUNT(DISTINCT c.id) as assigned_clients,
            COUNT(DISTINCT CASE WHEN c.status = 'active' THEN c.id END) as active_clients,
            COUNT(cl.id) as total_communications,
            COUNT(CASE WHEN cl.created_at BETWEEN ? AND ? THEN cl.id END) as period_communications,
            COUNT(CASE WHEN cl.communication_type = 'email' AND cl.created_at BETWEEN ? AND ? THEN cl.id END) as period_emails,
            COUNT(CASE WHEN cl.communication_type = 'whatsapp' AND cl.created_at BETWEEN ? AND ? THEN cl.id END) as period_whatsapp,
            COUNT(CASE WHEN cl.communication_type = 'phone' AND cl.created_at BETWEEN ? AND ? THEN cl.id END) as period_phone,
            COUNT(CASE WHEN cl.status = 'sent' AND cl.created_at BETWEEN ? AND ? THEN cl.id END) as period_sent,
            COUNT(CASE WHEN cl.status = 'failed' AND cl.created_at BETWEEN ? AND ? THEN cl.id END) as period_failed,
            MAX(cl.created_at) as last_communication,
            COUNT(CASE WHEN DATE(cl.created_at) = CURDATE() THEN cl.id END) as today_communications
            FROM users u
            LEFT JOIN clients c ON u.id = c.assigned_employee_id
            LEFT JOIN communication_log cl ON u.id = cl.employee_id
            WHERE $where_clause
            GROUP BY u.id
            ORDER BY period_communications DESC";

        $date_params = array_fill(0, 10, $date_from . ' 00:00:00');
        $date_params = array_merge($date_params, array_fill(0, 10, $date_to . ' 23:59:59'));
        $performance_params = array_merge($params, $date_params);

        $performance_stmt = $db->executeQuery($performance_query, $performance_params);
        $performance_data = $performance_stmt->fetchAll();
    }

    // تقرير نشاط الموظفين
    elseif ($report_type == 'activity') {
        $activity_query = "SELECT
            u.full_name,
            u.department,
            u.role,
            COUNT(ua.id) as total_activities,
            COUNT(CASE WHEN DATE(ua.created_at) BETWEEN ? AND ? THEN ua.id END) as period_activities,
            COUNT(CASE WHEN ua.activity_type = 'login' AND DATE(ua.created_at) BETWEEN ? AND ? THEN ua.id END) as login_count,
            COUNT(CASE WHEN ua.activity_type = 'send_email' AND DATE(ua.created_at) BETWEEN ? AND ? THEN ua.id END) as email_activities,
            COUNT(CASE WHEN ua.activity_type = 'send_whatsapp' AND DATE(ua.created_at) BETWEEN ? AND ? THEN ua.id END) as whatsapp_activities,
            COUNT(CASE WHEN DATE(ua.created_at) = CURDATE() THEN ua.id END) as today_activities,
            MAX(ua.created_at) as last_activity,
            u.last_login
            FROM users u
            LEFT JOIN user_activity ua ON u.id = ua.user_id
            WHERE $where_clause
            GROUP BY u.id
            ORDER BY period_activities DESC";

        $activity_params = array_merge($params, [
            $date_from, $date_to, $date_from, $date_to,
            $date_from, $date_to, $date_from, $date_to
        ]);

        $activity_stmt = $db->executeQuery($activity_query, $activity_params);
        $activity_data = $activity_stmt->fetchAll();
    }

    // تقرير الموظفين حسب القسم
    elseif ($report_type == 'by_department') {
        $department_query = "SELECT
            COALESCE(u.department, 'غير محدد') as department_name,
            COUNT(u.id) as total_employees,
            COUNT(CASE WHEN u.is_active = TRUE THEN u.id END) as active_employees,
            COUNT(CASE WHEN u.role = 'admin' THEN u.id END) as admin_count,
            COUNT(CASE WHEN u.role = 'supervisor' THEN u.id END) as supervisor_count,
            COUNT(CASE WHEN u.role = 'employee' THEN u.id END) as employee_count,
            COUNT(DISTINCT c.id) as total_assigned_clients,
            COUNT(cl.id) as total_communications,
            COUNT(CASE WHEN cl.created_at BETWEEN ? AND ? THEN cl.id END) as period_communications,
            AVG(CASE WHEN cl.created_at BETWEEN ? AND ? THEN 1 ELSE 0 END) as avg_communications_per_employee
            FROM users u
            LEFT JOIN clients c ON u.id = c.assigned_employee_id
            LEFT JOIN communication_log cl ON u.id = cl.employee_id
            WHERE $where_clause
            GROUP BY COALESCE(u.department, 'غير محدد')
            ORDER BY total_employees DESC";

        $dept_params = array_merge($params, [
            $date_from . ' 00:00:00', $date_to . ' 23:59:59',
            $date_from . ' 00:00:00', $date_to . ' 23:59:59'
        ]);

        $department_stmt = $db->executeQuery($department_query, $dept_params);
        $department_data = $department_stmt->fetchAll();
    }

    // قائمة الأقسام للتصفية
    $departments_query = "SELECT DISTINCT department FROM users WHERE department IS NOT NULL AND department != '' ORDER BY department";
    $departments_stmt = $db->executeQuery($departments_query);
    $departments = $departments_stmt->fetchAll();

    // إحصائيات عامة
    $stats_query = "SELECT
        COUNT(*) as total_employees,
        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_employees,
        COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_count,
        COUNT(CASE WHEN role = 'supervisor' THEN 1 END) as supervisor_count,
        COUNT(CASE WHEN role = 'employee' THEN 1 END) as employee_count,
        COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as active_last_week
        FROM users
        WHERE role IN ('employee', 'supervisor', 'admin')";

    $stats_stmt = $db->executeQuery($stats_query);
    $stats = $stats_stmt->fetch();

} catch (Exception $e) {
    Config::logError("Employee reports error: " . $e->getMessage());
    showAlert('حدث خطأ في تحميل التقارير', 'danger');
    $performance_data = [];
    $activity_data = [];
    $department_data = [];
    $departments = [];
    $stats = ['total_employees' => 0, 'active_employees' => 0, 'admin_count' => 0, 'supervisor_count' => 0, 'employee_count' => 0, 'active_last_week' => 0];
}

$page_title = "تقارير الموظفين";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">تقارير الموظفين</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="exportReport()">
                            <i class="fas fa-download me-1"></i>
                            تصدير
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>
                            طباعة
                        </button>
                    </div>
                </div>
            </div>

            <!-- إحصائيات عامة -->
            <div class="row mb-4">
                <div class="col-md-2 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($stats['total_employees']); ?></div>
                            <div>إجمالي الموظفين</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($stats['active_employees']); ?></div>
                            <div>نشطين</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($stats['admin_count']); ?></div>
                            <div>مديرين</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($stats['supervisor_count']); ?></div>
                            <div>مشرفين</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($stats['employee_count']); ?></div>
                            <div>موظفين</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 mb-3">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <div class="h3 mb-0"><?php echo number_format($stats['active_last_week']); ?></div>
                            <div>نشطين هذا الأسبوع</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج التصفية -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="report_type" class="form-label">نوع التقرير</label>
                            <select class="form-select" id="report_type" name="report_type">
                                <option value="performance" <?php echo $report_type == 'performance' ? 'selected' : ''; ?>>تقرير الأداء</option>
                                <option value="activity" <?php echo $report_type == 'activity' ? 'selected' : ''; ?>>تقرير النشاط</option>
                                <option value="by_department" <?php echo $report_type == 'by_department' ? 'selected' : ''; ?>>حسب القسم</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from"
                                   value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to"
                                   value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="department" class="form-label">القسم</label>
                            <select class="form-select" id="department" name="department">
                                <option value="">جميع الأقسام</option>
                                <?php foreach ($departments as $dept): ?>
                                <option value="<?php echo htmlspecialchars($dept['department']); ?>"
                                        <?php echo $department == $dept['department'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($dept['department']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="role" class="form-label">الصلاحية</label>
                            <select class="form-select" id="role" name="role">
                                <option value="">جميع الصلاحيات</option>
                                <option value="admin" <?php echo $role == 'admin' ? 'selected' : ''; ?>>مدير</option>
                                <option value="supervisor" <?php echo $role == 'supervisor' ? 'selected' : ''; ?>>مشرف</option>
                                <option value="employee" <?php echo $role == 'employee' ? 'selected' : ''; ?>>موظف</option>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>
                                    عرض
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <?php if ($report_type == 'performance'): ?>
            <!-- تقرير أداء الموظفين -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">تقرير أداء الموظفين</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>الموظف</th>
                                    <th>القسم</th>
                                    <th>الصلاحية</th>
                                    <th>العملاء المعينين</th>
                                    <th>العملاء النشطين</th>
                                    <th>تواصل الفترة</th>
                                    <th>بريد إلكتروني</th>
                                    <th>واتساب</th>
                                    <th>هاتف</th>
                                    <th>نجح</th>
                                    <th>فشل</th>
                                    <th>اليوم</th>
                                    <th>آخر تواصل</th>
                                    <th>آخر دخول</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($performance_data)): ?>
                                <tr>
                                    <td colspan="15" class="text-center py-4">
                                        <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد بيانات موظفين</p>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($performance_data as $emp): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($emp['full_name']); ?></strong>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($emp['email']); ?></small>
                                    </td>
                                    <td><?php echo htmlspecialchars($emp['department'] ?? 'غير محدد'); ?></td>
                                    <td>
                                        <?php
                                        $role_badges = [
                                            'admin' => '<span class="badge bg-danger">مدير</span>',
                                            'supervisor' => '<span class="badge bg-warning">مشرف</span>',
                                            'employee' => '<span class="badge bg-info">موظف</span>'
                                        ];
                                        echo $role_badges[$emp['role']] ?? $emp['role'];
                                        ?>
                                    </td>
                                    <td><span class="badge bg-primary"><?php echo number_format($emp['assigned_clients']); ?></span></td>
                                    <td><span class="badge bg-success"><?php echo number_format($emp['active_clients']); ?></span></td>
                                    <td><span class="badge bg-info fs-6"><?php echo number_format($emp['period_communications']); ?></span></td>
                                    <td><span class="badge bg-primary"><?php echo number_format($emp['period_emails']); ?></span></td>
                                    <td><span class="badge bg-success"><?php echo number_format($emp['period_whatsapp']); ?></span></td>
                                    <td><span class="badge bg-warning"><?php echo number_format($emp['period_phone']); ?></span></td>
                                    <td><span class="badge bg-success"><?php echo number_format($emp['period_sent']); ?></span></td>
                                    <td><span class="badge bg-danger"><?php echo number_format($emp['period_failed']); ?></span></td>
                                    <td><span class="badge bg-dark"><?php echo number_format($emp['today_communications']); ?></span></td>
                                    <td><?php echo $emp['last_communication'] ? formatDateArabic($emp['last_communication']) : 'لا يوجد'; ?></td>
                                    <td><?php echo $emp['last_login'] ? formatDateArabic($emp['last_login']) : 'لم يسجل دخول'; ?></td>
                                    <td>
                                        <?php if ($emp['is_active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">غير نشط</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <?php elseif ($report_type == 'activity'): ?>
            <!-- تقرير نشاط الموظفين -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">تقرير نشاط الموظفين</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>الموظف</th>
                                    <th>القسم</th>
                                    <th>الصلاحية</th>
                                    <th>إجمالي الأنشطة</th>
                                    <th>أنشطة الفترة</th>
                                    <th>مرات الدخول</th>
                                    <th>أنشطة البريد</th>
                                    <th>أنشطة واتساب</th>
                                    <th>أنشطة اليوم</th>
                                    <th>آخر نشاط</th>
                                    <th>آخر دخول</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($activity_data)): ?>
                                <tr>
                                    <td colspan="11" class="text-center py-4">
                                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد بيانات أنشطة</p>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($activity_data as $activity): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($activity['full_name']); ?></strong></td>
                                    <td><?php echo htmlspecialchars($activity['department'] ?? 'غير محدد'); ?></td>
                                    <td>
                                        <?php
                                        $role_badges = [
                                            'admin' => '<span class="badge bg-danger">مدير</span>',
                                            'supervisor' => '<span class="badge bg-warning">مشرف</span>',
                                            'employee' => '<span class="badge bg-info">موظف</span>'
                                        ];
                                        echo $role_badges[$activity['role']] ?? $activity['role'];
                                        ?>
                                    </td>
                                    <td><span class="badge bg-primary fs-6"><?php echo number_format($activity['total_activities']); ?></span></td>
                                    <td><span class="badge bg-info fs-6"><?php echo number_format($activity['period_activities']); ?></span></td>
                                    <td><span class="badge bg-success"><?php echo number_format($activity['login_count']); ?></span></td>
                                    <td><span class="badge bg-primary"><?php echo number_format($activity['email_activities']); ?></span></td>
                                    <td><span class="badge bg-success"><?php echo number_format($activity['whatsapp_activities']); ?></span></td>
                                    <td><span class="badge bg-dark"><?php echo number_format($activity['today_activities']); ?></span></td>
                                    <td><?php echo $activity['last_activity'] ? formatDateArabic($activity['last_activity']) : 'لا يوجد'; ?></td>
                                    <td><?php echo $activity['last_login'] ? formatDateArabic($activity['last_login']) : 'لم يسجل دخول'; ?></td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <?php elseif ($report_type == 'by_department'): ?>
            <!-- تقرير الموظفين حسب القسم -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">تقرير الموظفين حسب القسم</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>القسم</th>
                                    <th>إجمالي الموظفين</th>
                                    <th>نشطين</th>
                                    <th>مديرين</th>
                                    <th>مشرفين</th>
                                    <th>موظفين</th>
                                    <th>العملاء المعينين</th>
                                    <th>إجمالي التواصل</th>
                                    <th>تواصل الفترة</th>
                                    <th>متوسط التواصل</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($department_data)): ?>
                                <tr>
                                    <td colspan="10" class="text-center py-4">
                                        <i class="fas fa-building fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد بيانات أقسام</p>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($department_data as $dept): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($dept['department_name']); ?></strong></td>
                                    <td><span class="badge bg-primary fs-6"><?php echo number_format($dept['total_employees']); ?></span></td>
                                    <td><span class="badge bg-success"><?php echo number_format($dept['active_employees']); ?></span></td>
                                    <td><span class="badge bg-danger"><?php echo number_format($dept['admin_count']); ?></span></td>
                                    <td><span class="badge bg-warning"><?php echo number_format($dept['supervisor_count']); ?></span></td>
                                    <td><span class="badge bg-info"><?php echo number_format($dept['employee_count']); ?></span></td>
                                    <td><span class="badge bg-secondary"><?php echo number_format($dept['total_assigned_clients']); ?></span></td>
                                    <td><span class="badge bg-primary"><?php echo number_format($dept['total_communications']); ?></span></td>
                                    <td><span class="badge bg-info"><?php echo number_format($dept['period_communications']); ?></span></td>
                                    <td><span class="badge bg-dark"><?php echo number_format($dept['avg_communications_per_employee'], 1); ?></span></td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </main>
    </div>
</div>

<script>
function exportReport() {
    // تصدير التقرير كـ CSV
    const table = document.querySelector('.table');
    if (!table) return;

    let csv = '';
    const rows = table.querySelectorAll('tr');

    for (let i = 0; i < rows.length; i++) {
        const cols = rows[i].querySelectorAll('td, th');
        const rowData = [];

        for (let j = 0; j < cols.length; j++) {
            let cellData = cols[j].textContent.trim();
            cellData = cellData.replace(/"/g, '""'); // escape quotes
            rowData.push('"' + cellData + '"');
        }

        csv += rowData.join(',') + '\n';
    }

    // تحميل الملف
    const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'employee_report_<?php echo date('Y-m-d'); ?>.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// تحديث التقرير عند تغيير النوع
document.getElementById('report_type').addEventListener('change', function() {
    this.form.submit();
});
</script>

<?php include 'includes/footer.php'; ?>