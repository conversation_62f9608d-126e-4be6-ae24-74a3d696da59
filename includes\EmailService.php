<?php
/**
 * خدمة البريد الإلكتروني - نظام التواصل الذكي
 * Email Service - Smart Communication System
 */

// تحميل PHPMailer - سيتم استخدام نسخة محلية إذا لم يكن Composer متوفراً
if (file_exists(__DIR__ . '/../vendor/autoload.php')) {
    require_once __DIR__ . '/../vendor/autoload.php';
    use PHPMailer\PHPMailer\PHPMailer;
    use PHPMailer\PHPMailer\SMTP;
    use PHPMailer\PHPMailer\Exception;
    define('PHPMAILER_AVAILABLE', true);
} else {
    // استخدام نسخة محلية مبسطة من PHPMailer
    define('PHPMAILER_AVAILABLE', false);
}

class EmailService {
    private $mailer;
    private $db;
    private $use_phpmailer;

    public function __construct() {
        $this->db = new Database();
        $this->use_phpmailer = PHPMAILER_AVAILABLE;

        if ($this->use_phpmailer) {
            $this->mailer = new PHPMailer(true);
            $this->setupMailer();
        }
    }
    
    /**
     * إعداد PHPMailer
     */
    private function setupMailer() {
        try {
            // إعدادات الخادم
            $this->mailer->isSMTP();
            $this->mailer->Host = getSystemSetting('smtp_host', Config::SMTP_HOST);
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = getSystemSetting('smtp_username', Config::SMTP_USERNAME);
            $this->mailer->Password = getSystemSetting('smtp_password', Config::SMTP_PASSWORD);
            $this->mailer->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $this->mailer->Port = (int)getSystemSetting('smtp_port', Config::SMTP_PORT);
            
            // إعدادات الترميز
            $this->mailer->CharSet = 'UTF-8';
            $this->mailer->Encoding = 'base64';
            
            // إعدادات المرسل الافتراضي
            $from_email = getSystemSetting('smtp_username', Config::SMTP_USERNAME);
            $from_name = getSystemSetting('company_name', Config::COMPANY_NAME);
            
            if (!empty($from_email)) {
                $this->mailer->setFrom($from_email, $from_name);
            }
            
        } catch (Exception $e) {
            Config::logError("Email setup error: " . $e->getMessage());
            throw new Exception("فشل في إعداد خدمة البريد الإلكتروني");
        }
    }
    
    /**
     * إرسال بريد إلكتروني
     */
    public function sendEmail($to_email, $to_name, $subject, $body, $client_id = null, $employee_id = null, $template_id = null) {
        try {
            // تنظيف البيانات
            $to_email = filter_var($to_email, FILTER_SANITIZE_EMAIL);
            $to_name = Config::sanitizeInput($to_name);
            $subject = Config::sanitizeInput($subject);

            if (!filter_var($to_email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception("البريد الإلكتروني غير صحيح");
            }

            if ($this->use_phpmailer) {
                // استخدام PHPMailer
                return $this->sendWithPHPMailer($to_email, $to_name, $subject, $body, $client_id, $employee_id, $template_id);
            } else {
                // استخدام mail() function المدمجة في PHP
                return $this->sendWithPHPMail($to_email, $to_name, $subject, $body, $client_id, $employee_id, $template_id);
            }

        } catch (Exception $e) {
            Config::logError("Email send error: " . $e->getMessage());

            // حفظ سجل الفشل
            if ($client_id && $employee_id) {
                $this->logCommunication($client_id, $employee_id, 'email', $subject, $body, 'failed', $template_id, $e->getMessage());
            }

            return [
                'success' => false,
                'message' => 'فشل في إرسال البريد الإلكتروني: ' . $e->getMessage()
            ];
        }
    }

    /**
     * إرسال بريد إلكتروني باستخدام PHPMailer
     */
    private function sendWithPHPMailer($to_email, $to_name, $subject, $body, $client_id, $employee_id, $template_id) {
        // إعداد المستقبل
        $this->mailer->clearAddresses();
        $this->mailer->addAddress($to_email, $to_name);

        // إعداد الموضوع والمحتوى
        $this->mailer->Subject = $subject;
        $this->mailer->isHTML(true);
        $this->mailer->Body = $this->formatEmailBody($body);
        $this->mailer->AltBody = strip_tags($body);

        // إرسال البريد
        $sent = $this->mailer->send();

        if ($sent) {
            // حفظ السجل في قاعدة البيانات
            $this->logCommunication($client_id, $employee_id, 'email', $subject, $body, 'sent', $template_id);

            return [
                'success' => true,
                'message' => 'تم إرسال البريد الإلكتروني بنجاح (PHPMailer)'
            ];
        } else {
            throw new Exception("فشل في إرسال البريد الإلكتروني");
        }
    }

    /**
     * إرسال بريد إلكتروني باستخدام mail() function
     */
    private function sendWithPHPMail($to_email, $to_name, $subject, $body, $client_id, $employee_id, $template_id) {
        // إعداد الرؤوس
        $headers = [];
        $headers[] = 'MIME-Version: 1.0';
        $headers[] = 'Content-type: text/html; charset=UTF-8';
        $headers[] = 'From: ' . getSystemSetting('company_name', Config::COMPANY_NAME) . ' <<EMAIL>>';
        $headers[] = 'Reply-To: <EMAIL>';
        $headers[] = 'X-Mailer: Smart Communication System';

        // تنسيق المحتوى
        $formatted_body = $this->formatEmailBody($body);

        // إرسال البريد
        $sent = mail($to_email, $subject, $formatted_body, implode("\r\n", $headers));

        if ($sent) {
            // حفظ السجل في قاعدة البيانات
            $this->logCommunication($client_id, $employee_id, 'email', $subject, $body, 'sent', $template_id);

            return [
                'success' => true,
                'message' => 'تم إرسال البريد الإلكتروني بنجاح (PHP Mail)'
            ];
        } else {
            throw new Exception("فشل في إرسال البريد الإلكتروني باستخدام PHP mail()");
        }
    }
    
    /**
     * إرسال بريد جماعي
     */
    public function sendBulkEmail($recipients, $subject, $body, $employee_id, $template_id = null) {
        $results = [
            'success' => 0,
            'failed' => 0,
            'errors' => []
        ];
        
        foreach ($recipients as $recipient) {
            $result = $this->sendEmail(
                $recipient['email'],
                $recipient['name'],
                $subject,
                $body,
                $recipient['client_id'] ?? null,
                $employee_id,
                $template_id
            );
            
            if ($result['success']) {
                $results['success']++;
            } else {
                $results['failed']++;
                $results['errors'][] = $recipient['name'] . ': ' . $result['message'];
            }
            
            // توقف قصير لتجنب الحظر
            usleep(500000); // 0.5 ثانية
        }
        
        return $results;
    }
    
    /**
     * تنسيق محتوى البريد الإلكتروني
     */
    private function formatEmailBody($body) {
        // قالب HTML أساسي
        $html_template = '
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>رسالة من ' . Config::COMPANY_NAME . '</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    background-color: #f4f4f4;
                    margin: 0;
                    padding: 20px;
                }
                .container {
                    max-width: 600px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 10px;
                    overflow: hidden;
                    box-shadow: 0 0 20px rgba(0,0,0,0.1);
                }
                .header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px;
                    text-align: center;
                }
                .content {
                    padding: 30px;
                }
                .footer {
                    background: #f8f9fa;
                    padding: 20px;
                    text-align: center;
                    color: #666;
                    font-size: 14px;
                }
                .logo {
                    font-size: 24px;
                    font-weight: bold;
                    margin-bottom: 10px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="logo">' . Config::COMPANY_NAME . '</div>
                    <div>' . Config::SITE_NAME . '</div>
                </div>
                <div class="content">
                    ' . nl2br(htmlspecialchars($body)) . '
                </div>
                <div class="footer">
                    <p>&copy; ' . date('Y') . ' ' . Config::COMPANY_NAME . '. جميع الحقوق محفوظة.</p>
                    <p>هذه رسالة تلقائية، يرجى عدم الرد عليها مباشرة.</p>
                </div>
            </div>
        </body>
        </html>';
        
        return $html_template;
    }
    
    /**
     * تسجيل التواصل في قاعدة البيانات
     */
    private function logCommunication($client_id, $employee_id, $type, $subject, $message, $status, $template_id = null, $error_message = null) {
        try {
            $query = "INSERT INTO communication_log 
                     (client_id, employee_id, communication_type, subject, message, status, sent_at, created_at) 
                     VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())";
            
            $params = [$client_id, $employee_id, $type, $subject, $message, $status];
            
            $this->db->executeQuery($query, $params);
            
            // تسجيل الخطأ إذا وجد
            if ($error_message) {
                Config::logError("Communication failed: $error_message");
            }
            
        } catch (Exception $e) {
            Config::logError("Log communication error: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على قوالب البريد الإلكتروني
     */
    public function getEmailTemplates($user_id = null) {
        try {
            $query = "SELECT * FROM message_templates WHERE type = 'email' AND is_active = TRUE";
            $params = [];
            
            if ($user_id) {
                $query .= " AND (created_by = ? OR created_by IN (SELECT id FROM users WHERE role = 'admin'))";
                $params[] = $user_id;
            }
            
            $query .= " ORDER BY name";
            
            $stmt = $this->db->executeQuery($query, $params);
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            Config::logError("Get email templates error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * معالجة المتغيرات في القالب
     */
    public function processTemplate($template_content, $variables) {
        $processed_content = $template_content;
        
        foreach ($variables as $key => $value) {
            $processed_content = str_replace('{{' . $key . '}}', $value, $processed_content);
        }
        
        return $processed_content;
    }
    
    /**
     * التحقق من إعدادات البريد الإلكتروني
     */
    public function testEmailSettings() {
        try {
            if ($this->use_phpmailer) {
                // اختبار PHPMailer
                $this->mailer->smtpConnect();
                $this->mailer->smtpClose();

                return [
                    'success' => true,
                    'message' => 'إعدادات البريد الإلكتروني صحيحة (PHPMailer)'
                ];
            } else {
                // اختبار PHP mail() function
                if (function_exists('mail')) {
                    return [
                        'success' => true,
                        'message' => 'PHP mail() function متوفرة (تحتاج إعداد SMTP في الخادم)'
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'PHP mail() function غير متوفرة'
                    ];
                }
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في إعدادات البريد الإلكتروني: ' . $e->getMessage()
            ];
        }
    }
}
?>
