<?php
/**
 * فئة الأمان والحماية - نظام التواصل الذكي
 * Security Class - Smart Communication System
 */

class Security {
    
    /**
     * تشفير البيانات الحساسة
     */
    public static function encryptData($data, $key = null) {
        if ($key === null) {
            $key = Config::ENCRYPTION_KEY;
        }
        
        $key = hash('sha256', $key);
        $iv = openssl_random_pseudo_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        
        return base64_encode($encrypted . '::' . $iv);
    }
    
    /**
     * فك تشفير البيانات
     */
    public static function decryptData($data, $key = null) {
        if ($key === null) {
            $key = Config::ENCRYPTION_KEY;
        }
        
        $key = hash('sha256', $key);
        list($encrypted_data, $iv) = explode('::', base64_decode($data), 2);
        
        return openssl_decrypt($encrypted_data, 'AES-256-CBC', $key, 0, $iv);
    }
    
    /**
     * تنظيف البيانات المدخلة من XSS
     */
    public static function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitizeInput'], $input);
        }
        
        // إزالة الرموز الخطيرة
        $input = trim($input);
        $input = stripslashes($input);
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        
        return $input;
    }
    
    /**
     * التحقق من صحة البريد الإلكتروني
     */
    public static function validateEmail($email) {
        $email = filter_var($email, FILTER_SANITIZE_EMAIL);
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * التحقق من قوة كلمة المرور
     */
    public static function validatePassword($password) {
        $errors = [];
        
        if (strlen($password) < 8) {
            $errors[] = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';
        }
        
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل';
        }
        
        return empty($errors) ? true : $errors;
    }
    
    /**
     * إنشاء رمز CSRF آمن
     */
    public static function generateCSRFToken() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        return $_SESSION['csrf_token'];
    }
    
    /**
     * التحقق من رمز CSRF
     */
    public static function verifyCSRFToken($token) {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * تسجيل محاولات تسجيل الدخول الفاشلة
     */
    public static function logFailedLogin($username, $ip_address) {
        try {
            $db = new Database();
            $query = "INSERT INTO failed_login_attempts (username, ip_address, attempt_time) VALUES (?, ?, NOW())";
            $db->executeQuery($query, [$username, $ip_address]);
            
            // حذف المحاولات القديمة (أكثر من 24 ساعة)
            $cleanup_query = "DELETE FROM failed_login_attempts WHERE attempt_time < DATE_SUB(NOW(), INTERVAL 24 HOUR)";
            $db->executeQuery($cleanup_query);
            
        } catch (Exception $e) {
            Config::logError("Failed to log failed login attempt: " . $e->getMessage());
        }
    }
    
    /**
     * التحقق من محاولات تسجيل الدخول المتكررة
     */
    public static function checkBruteForce($username, $ip_address, $max_attempts = 5, $lockout_time = 900) {
        try {
            $db = new Database();
            
            // عدد المحاولات الفاشلة في آخر 15 دقيقة
            $query = "SELECT COUNT(*) as attempts FROM failed_login_attempts 
                     WHERE (username = ? OR ip_address = ?) 
                     AND attempt_time > DATE_SUB(NOW(), INTERVAL ? SECOND)";
            
            $stmt = $db->executeQuery($query, [$username, $ip_address, $lockout_time]);
            $result = $stmt->fetch();
            
            return $result['attempts'] >= $max_attempts;
            
        } catch (Exception $e) {
            Config::logError("Failed to check brute force: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * تنظيف اسم الملف من الرموز الخطيرة
     */
    public static function sanitizeFilename($filename) {
        // إزالة المسارات
        $filename = basename($filename);
        
        // إزالة الرموز الخطيرة
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);
        
        // منع الملفات المخفية
        $filename = ltrim($filename, '.');
        
        // تحديد طول الاسم
        if (strlen($filename) > 255) {
            $filename = substr($filename, 0, 255);
        }
        
        return $filename;
    }
    
    /**
     * التحقق من نوع الملف المسموح
     */
    public static function validateFileType($filename, $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx']) {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        return in_array($extension, $allowed_types);
    }
    
    /**
     * إنشاء كلمة مرور عشوائية آمنة
     */
    public static function generateSecurePassword($length = 12) {
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '0123456789';
        $symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
        
        $all_chars = $uppercase . $lowercase . $numbers . $symbols;
        
        $password = '';
        
        // ضمان وجود حرف من كل نوع
        $password .= $uppercase[random_int(0, strlen($uppercase) - 1)];
        $password .= $lowercase[random_int(0, strlen($lowercase) - 1)];
        $password .= $numbers[random_int(0, strlen($numbers) - 1)];
        $password .= $symbols[random_int(0, strlen($symbols) - 1)];
        
        // إكمال باقي الطول
        for ($i = 4; $i < $length; $i++) {
            $password .= $all_chars[random_int(0, strlen($all_chars) - 1)];
        }
        
        // خلط الأحرف
        return str_shuffle($password);
    }
    
    /**
     * تشفير كلمة المرور
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536,
            'time_cost' => 4,
            'threads' => 3
        ]);
    }
    
    /**
     * التحقق من كلمة المرور
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * تنظيف SQL من الحقن
     */
    public static function sanitizeSQL($input) {
        // هذه الوظيفة للحماية الإضافية، لكن يجب استخدام Prepared Statements دائماً
        $input = str_replace(['--', ';', '/*', '*/', 'xp_', 'sp_'], '', $input);
        $input = preg_replace('/\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b/i', '', $input);
        
        return $input;
    }
    
    /**
     * التحقق من عنوان IP
     */
    public static function validateIP($ip) {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }
    
    /**
     * الحصول على عنوان IP الحقيقي
     */
    public static function getRealIP() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (isset($_SERVER[$key]) && !empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                
                if (self::validateIP($ip) && !self::isPrivateIP($ip)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * التحقق من IP خاص
     */
    public static function isPrivateIP($ip) {
        return !filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE);
    }
    
    /**
     * تسجيل نشاط مشبوه
     */
    public static function logSuspiciousActivity($activity, $details = '', $user_id = null) {
        try {
            $db = new Database();
            $query = "INSERT INTO security_log (user_id, activity, details, ip_address, user_agent, created_at) 
                     VALUES (?, ?, ?, ?, ?, NOW())";
            
            $params = [
                $user_id,
                $activity,
                $details,
                self::getRealIP(),
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ];
            
            $db->executeQuery($query, $params);
            
        } catch (Exception $e) {
            Config::logError("Failed to log suspicious activity: " . $e->getMessage());
        }
    }
    
    /**
     * فحص الجلسة للتأكد من صحتها
     */
    public static function validateSession() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        // التحقق من انتهاء صلاحية الجلسة
        if (isset($_SESSION['login_time'])) {
            $session_lifetime = Config::SESSION_TIMEOUT;
            if (time() - $_SESSION['login_time'] > $session_lifetime) {
                session_destroy();
                return false;
            }
        }
        
        // التحقق من تغيير IP (اختياري)
        if (isset($_SESSION['ip_address'])) {
            $current_ip = self::getRealIP();
            if ($_SESSION['ip_address'] !== $current_ip) {
                // يمكن تسجيل هذا كنشاط مشبوه
                self::logSuspiciousActivity('ip_change', "Old: {$_SESSION['ip_address']}, New: $current_ip", $_SESSION['user_id'] ?? null);
            }
        }
        
        return true;
    }
    
    /**
     * تنظيف المخرجات لمنع XSS
     */
    public static function cleanOutput($output) {
        return htmlspecialchars($output, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * التحقق من الصلاحيات
     */
    public static function checkPermission($required_permission, $user_permissions) {
        if (is_array($user_permissions)) {
            return in_array($required_permission, $user_permissions);
        }
        
        return $user_permissions === $required_permission;
    }
}

// إنشاء جدول محاولات تسجيل الدخول الفاشلة إذا لم يكن موجوداً
try {
    $db = new Database();
    $create_table_query = "
        CREATE TABLE IF NOT EXISTS failed_login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(100),
            ip_address VARCHAR(45),
            attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_username (username),
            INDEX idx_ip (ip_address),
            INDEX idx_time (attempt_time)
        )
    ";
    $db->executeQuery($create_table_query);
    
    // إنشاء جدول سجل الأمان
    $security_log_query = "
        CREATE TABLE IF NOT EXISTS security_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            activity VARCHAR(100) NOT NULL,
            details TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_activity (activity),
            INDEX idx_created_at (created_at)
        )
    ";
    $db->executeQuery($security_log_query);
    
} catch (Exception $e) {
    Config::logError("Failed to create security tables: " . $e->getMessage());
}
?>
