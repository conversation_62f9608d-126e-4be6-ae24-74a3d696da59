<?php
/**
 * خدمة البريد الإلكتروني المبسطة - نظام التواصل الذكي
 * Simple Email Service - Smart Communication System
 * تدعم SMTP وتعمل مع Gmail وخوادم البريد الأخرى
 */

class SimpleEmailService {
    private $db;
    private $smtp_enabled;
    private $smtp_host;
    private $smtp_port;
    private $smtp_username;
    private $smtp_password;
    private $smtp_secure;

    public function __construct() {
        $this->db = new Database();
        $this->loadSMTPSettings();
    }

    /**
     * تحميل إعدادات SMTP
     */
    private function loadSMTPSettings() {
        $this->smtp_enabled = getSystemSetting('smtp_enabled', 'false') === 'true';
        $this->smtp_host = getSystemSetting('smtp_host', 'smtp.gmail.com');
        $this->smtp_port = (int)getSystemSetting('smtp_port', 587);
        $this->smtp_username = getSystemSetting('smtp_username', '');
        $this->smtp_password = getSystemSetting('smtp_password', '');
        $this->smtp_secure = getSystemSetting('smtp_secure', 'tls');
    }
    
    /**
     * إرسال بريد إلكتروني
     */
    public function sendEmail($to_email, $to_name, $subject, $body, $client_id = null, $employee_id = null, $template_id = null) {
        try {
            // تنظيف البيانات
            $to_email = filter_var($to_email, FILTER_SANITIZE_EMAIL);
            $to_name = Config::sanitizeInput($to_name);
            $subject = Config::sanitizeInput($subject);

            if (!filter_var($to_email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception("البريد الإلكتروني غير صحيح");
            }

            // تنسيق المحتوى
            $formatted_body = $this->formatEmailBody($body);

            // إرسال البريد
            $sent = false;

            if ($this->smtp_enabled && !empty($this->smtp_username)) {
                // استخدام SMTP
                $sent = $this->sendViaSMTP($to_email, $to_name, $subject, $formatted_body);
            } else {
                // استخدام PHP mail() function (محاكاة الإرسال)
                $sent = $this->sendViaSimulation($to_email, $to_name, $subject, $formatted_body);
            }

            if ($sent) {
                // حفظ السجل في قاعدة البيانات
                $this->logCommunication($client_id, $employee_id, 'email', $subject, $body, 'sent', $template_id);

                return [
                    'success' => true,
                    'message' => 'تم إرسال البريد الإلكتروني بنجاح'
                ];
            } else {
                throw new Exception("فشل في إرسال البريد الإلكتروني");
            }

        } catch (Exception $e) {
            Config::logError("Email send error: " . $e->getMessage());

            // حفظ سجل الفشل
            if ($client_id && $employee_id) {
                $this->logCommunication($client_id, $employee_id, 'email', $subject, $body, 'failed', $template_id, $e->getMessage());
            }

            return [
                'success' => false,
                'message' => 'فشل في إرسال البريد الإلكتروني: ' . $e->getMessage()
            ];
        }
    }

    /**
     * إرسال عبر SMTP
     */
    private function sendViaSMTP($to_email, $to_name, $subject, $body) {
        try {
            // التحقق من إعدادات SMTP
            if (empty($this->smtp_host) || empty($this->smtp_username) || empty($this->smtp_password)) {
                throw new Exception("إعدادات SMTP غير مكتملة");
            }

            // محاولة الاتصال مع timeout قصير للاختبار
            $context = stream_context_create([
                'socket' => [
                    'timeout' => 5, // 5 ثواني timeout
                ]
            ]);

            $socket = @stream_socket_client(
                "tcp://{$this->smtp_host}:{$this->smtp_port}",
                $errno,
                $errstr,
                5,
                STREAM_CLIENT_CONNECT,
                $context
            );

            if (!$socket) {
                // في حالة فشل الاتصال، استخدم وضع المحاكاة
                Config::logError("SMTP connection failed: $errstr ($errno) - switching to simulation mode");
                return $this->sendViaSimulation($to_email, $to_name, $subject, $body);
            }

            // قراءة الرد الأولي
            $response = fgets($socket, 512);
            if (substr($response, 0, 3) != '220') {
                throw new Exception("خطأ في خادم SMTP: $response");
            }

            // EHLO
            fputs($socket, "EHLO " . $_SERVER['SERVER_NAME'] . "\r\n");
            $response = fgets($socket, 512);

            // STARTTLS (إذا كان مطلوباً)
            if ($this->smtp_secure == 'tls') {
                fputs($socket, "STARTTLS\r\n");
                $response = fgets($socket, 512);
                if (substr($response, 0, 3) != '220') {
                    throw new Exception("فشل في تفعيل TLS: $response");
                }

                // تفعيل التشفير
                if (!stream_socket_enable_crypto($socket, true, STREAM_CRYPTO_METHOD_TLS_CLIENT)) {
                    throw new Exception("فشل في تفعيل التشفير");
                }

                // EHLO مرة أخرى بعد TLS
                fputs($socket, "EHLO " . $_SERVER['SERVER_NAME'] . "\r\n");
                $response = fgets($socket, 512);
            }

            // المصادقة
            fputs($socket, "AUTH LOGIN\r\n");
            $response = fgets($socket, 512);
            if (substr($response, 0, 3) != '334') {
                throw new Exception("فشل في بدء المصادقة: $response");
            }

            // اسم المستخدم
            fputs($socket, base64_encode($this->smtp_username) . "\r\n");
            $response = fgets($socket, 512);
            if (substr($response, 0, 3) != '334') {
                throw new Exception("اسم المستخدم غير صحيح: $response");
            }

            // كلمة المرور
            fputs($socket, base64_encode($this->smtp_password) . "\r\n");
            $response = fgets($socket, 512);
            if (substr($response, 0, 3) != '235') {
                throw new Exception("كلمة المرور غير صحيحة: $response");
            }

            // FROM
            fputs($socket, "MAIL FROM: <{$this->smtp_username}>\r\n");
            $response = fgets($socket, 512);
            if (substr($response, 0, 3) != '250') {
                throw new Exception("خطأ في MAIL FROM: $response");
            }

            // TO
            fputs($socket, "RCPT TO: <$to_email>\r\n");
            $response = fgets($socket, 512);
            if (substr($response, 0, 3) != '250') {
                throw new Exception("خطأ في RCPT TO: $response");
            }

            // DATA
            fputs($socket, "DATA\r\n");
            $response = fgets($socket, 512);
            if (substr($response, 0, 3) != '354') {
                throw new Exception("خطأ في DATA: $response");
            }

            // الرؤوس والمحتوى
            $headers = "From: " . getSystemSetting('company_name', Config::COMPANY_NAME) . " <{$this->smtp_username}>\r\n";
            $headers .= "To: $to_name <$to_email>\r\n";
            $headers .= "Subject: $subject\r\n";
            $headers .= "MIME-Version: 1.0\r\n";
            $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
            $headers .= "X-Mailer: Smart Communication System\r\n";
            $headers .= "\r\n";

            fputs($socket, $headers . $body . "\r\n.\r\n");
            $response = fgets($socket, 512);
            if (substr($response, 0, 3) != '250') {
                throw new Exception("فشل في إرسال البيانات: $response");
            }

            // QUIT
            fputs($socket, "QUIT\r\n");
            fclose($socket);

            return true;

        } catch (Exception $e) {
            if (isset($socket) && $socket) {
                fclose($socket);
            }
            throw $e;
        }
    }

    /**
     * محاكاة الإرسال (للاختبار بدون SMTP)
     */
    private function sendViaSimulation($to_email, $to_name, $subject, $body) {
        try {
            // إنشاء مجلد logs إذا لم يكن موجوداً
            $log_file = 'logs/email_simulation.log';
            $log_dir = dirname($log_file);

            if (!is_dir($log_dir)) {
                if (!mkdir($log_dir, 0755, true)) {
                    Config::logError("Failed to create logs directory");
                    return false;
                }
            }

            // إعداد بيانات السجل
            $log_entry = [
                'timestamp' => date('Y-m-d H:i:s'),
                'to_email' => $to_email,
                'to_name' => $to_name,
                'subject' => $subject,
                'body_preview' => substr(strip_tags($body), 0, 100) . '...',
                'body_length' => strlen($body),
                'status' => 'simulated_success',
                'method' => 'simulation',
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ];

            // حفظ في ملف السجل
            $log_line = date('Y-m-d H:i:s') . " [SIMULATION] إرسال إلى: $to_email | الموضوع: $subject | الحالة: نجح (محاكاة)\n";

            if (file_put_contents($log_file, $log_line, FILE_APPEND | LOCK_EX) === false) {
                Config::logError("Failed to write to email simulation log");
                return false;
            }

            // حفظ تفاصيل JSON في ملف منفصل
            $json_file = 'logs/email_simulation_details.json';
            $existing_data = [];

            if (file_exists($json_file)) {
                $existing_content = file_get_contents($json_file);
                $existing_data = json_decode($existing_content, true) ?: [];
            }

            $existing_data[] = $log_entry;

            // الاحتفاظ بآخر 100 رسالة فقط
            if (count($existing_data) > 100) {
                $existing_data = array_slice($existing_data, -100);
            }

            file_put_contents($json_file, json_encode($existing_data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

            // إرجاع true لمحاكاة الإرسال الناجح
            return true;

        } catch (Exception $e) {
            Config::logError("Email simulation error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إرسال بريد جماعي
     */
    public function sendBulkEmail($recipients, $subject, $body, $employee_id, $template_id = null) {
        $results = [
            'success' => 0,
            'failed' => 0,
            'errors' => []
        ];
        
        foreach ($recipients as $recipient) {
            $result = $this->sendEmail(
                $recipient['email'],
                $recipient['name'],
                $subject,
                $body,
                $recipient['client_id'] ?? null,
                $employee_id,
                $template_id
            );
            
            if ($result['success']) {
                $results['success']++;
            } else {
                $results['failed']++;
                $results['errors'][] = $recipient['name'] . ': ' . $result['message'];
            }
            
            // توقف قصير لتجنب الحظر
            usleep(500000); // 0.5 ثانية
        }
        
        return $results;
    }
    
    /**
     * تنسيق محتوى البريد الإلكتروني
     */
    private function formatEmailBody($body) {
        // قالب HTML أساسي
        $html_template = '
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>رسالة من ' . Config::COMPANY_NAME . '</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    background-color: #f4f4f4;
                    margin: 0;
                    padding: 20px;
                }
                .container {
                    max-width: 600px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 10px;
                    overflow: hidden;
                    box-shadow: 0 0 20px rgba(0,0,0,0.1);
                }
                .header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px;
                    text-align: center;
                }
                .content {
                    padding: 30px;
                }
                .footer {
                    background: #f8f9fa;
                    padding: 20px;
                    text-align: center;
                    color: #666;
                    font-size: 14px;
                }
                .logo {
                    font-size: 24px;
                    font-weight: bold;
                    margin-bottom: 10px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="logo">' . Config::COMPANY_NAME . '</div>
                    <div>' . Config::SITE_NAME . '</div>
                </div>
                <div class="content">
                    ' . nl2br(htmlspecialchars($body)) . '
                </div>
                <div class="footer">
                    <p>&copy; ' . date('Y') . ' ' . Config::COMPANY_NAME . '. جميع الحقوق محفوظة.</p>
                    <p>هذه رسالة تلقائية، يرجى عدم الرد عليها مباشرة.</p>
                </div>
            </div>
        </body>
        </html>';
        
        return $html_template;
    }
    
    /**
     * تسجيل التواصل في قاعدة البيانات
     */
    private function logCommunication($client_id, $employee_id, $type, $subject, $message, $status, $template_id = null, $error_message = null) {
        try {
            $query = "INSERT INTO communication_log 
                     (client_id, employee_id, communication_type, subject, message, status, sent_at, created_at) 
                     VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())";
            
            $params = [$client_id, $employee_id, $type, $subject, $message, $status];
            
            $this->db->executeQuery($query, $params);
            
            // تسجيل الخطأ إذا وجد
            if ($error_message) {
                Config::logError("Communication failed: $error_message");
            }
            
        } catch (Exception $e) {
            Config::logError("Log communication error: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على قوالب البريد الإلكتروني
     */
    public function getEmailTemplates($user_id = null) {
        try {
            $query = "SELECT * FROM message_templates WHERE type = 'email' AND is_active = TRUE";
            $params = [];
            
            if ($user_id) {
                $query .= " AND (created_by = ? OR created_by IN (SELECT id FROM users WHERE role = 'admin'))";
                $params[] = $user_id;
            }
            
            $query .= " ORDER BY name";
            
            $stmt = $this->db->executeQuery($query, $params);
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            Config::logError("Get email templates error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * معالجة المتغيرات في القالب
     */
    public function processTemplate($template_content, $variables) {
        $processed_content = $template_content;
        
        foreach ($variables as $key => $value) {
            $processed_content = str_replace('{{' . $key . '}}', $value, $processed_content);
        }
        
        return $processed_content;
    }
    
    /**
     * التحقق من إعدادات البريد الإلكتروني
     */
    public function testEmailSettings() {
        try {
            if ($this->smtp_enabled && !empty($this->smtp_username)) {
                // اختبار SMTP
                $smtp_result = $this->testSMTPConnection();

                if (!$smtp_result['success']) {
                    // إذا فشل SMTP، اقترح وضع المحاكاة
                    return [
                        'success' => true,
                        'message' => $smtp_result['message'] . ' - سيتم التبديل تلقائياً لوضع المحاكاة عند الإرسال'
                    ];
                }

                return $smtp_result;
            } else {
                // وضع المحاكاة
                return [
                    'success' => true,
                    'message' => 'وضع المحاكاة مفعل - سيتم حفظ الرسائل في ملف السجل بدلاً من الإرسال الفعلي'
                ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في إعدادات البريد الإلكتروني: ' . $e->getMessage()
            ];
        }
    }

    /**
     * اختبار اتصال SMTP
     */
    private function testSMTPConnection() {
        try {
            // التحقق من الإعدادات أولاً
            if (empty($this->smtp_host) || empty($this->smtp_username)) {
                return [
                    'success' => false,
                    'message' => 'إعدادات SMTP غير مكتملة - يرجى إدخال الخادم واسم المستخدم'
                ];
            }

            // محاولة اتصال سريع
            $context = stream_context_create([
                'socket' => [
                    'timeout' => 3, // timeout قصير للاختبار
                ]
            ]);

            $socket = @stream_socket_client(
                "tcp://{$this->smtp_host}:{$this->smtp_port}",
                $errno,
                $errstr,
                3,
                STREAM_CLIENT_CONNECT,
                $context
            );

            if (!$socket) {
                return [
                    'success' => false,
                    'message' => "فشل في الاتصال بخادم SMTP: $errstr ($errno) - تحقق من إعدادات الشبكة أو استخدم وضع المحاكاة"
                ];
            }

            // قراءة الرد الأولي
            stream_set_timeout($socket, 3);
            $response = @fgets($socket, 512);
            fclose($socket);

            if ($response && substr($response, 0, 3) == '220') {
                return [
                    'success' => true,
                    'message' => 'تم الاتصال بخادم SMTP بنجاح - جاهز للإرسال'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => "رد غير متوقع من خادم SMTP: " . ($response ?: 'لا يوجد رد')
                ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في اختبار SMTP: ' . $e->getMessage() . ' - يمكنك استخدام وضع المحاكاة'
            ];
        }
    }

    /**
     * إرسال بريد اختبار
     */
    public function sendTestEmail($to_email, $to_name = 'مستخدم الاختبار') {
        $subject = 'رسالة اختبار من نظام التواصل الذكي';
        $body = 'هذه رسالة اختبار للتأكد من عمل نظام البريد الإلكتروني بشكل صحيح.';

        return $this->sendEmail($to_email, $to_name, $subject, $body);
    }
}
?>
