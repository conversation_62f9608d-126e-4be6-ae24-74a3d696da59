<?php
/**
 * خدمة واتساب - نظام التواصل الذكي
 * WhatsApp Service - Smart Communication System
 */

class WhatsAppService {
    private $api_url;
    private $api_token;
    private $db;
    
    public function __construct() {
        $this->db = new Database();
        $this->api_url = getSystemSetting('whatsapp_api_url', Config::WHATSAPP_API_URL);
        $this->api_token = getSystemSetting('whatsapp_api_token', Config::WHATSAPP_API_TOKEN);
    }
    
    /**
     * إرسال رسالة واتساب
     */
    public function sendMessage($phone_number, $message, $client_id = null, $employee_id = null, $template_id = null) {
        try {
            // تنظيف رقم الهاتف
            $phone_number = $this->formatPhoneNumber($phone_number);
            
            if (!$this->validatePhoneNumber($phone_number)) {
                throw new Exception("رقم الهاتف غير صحيح");
            }
            
            // تنظيف الرسالة
            $message = Config::sanitizeInput($message);
            
            if (empty($message)) {
                throw new Exception("محتوى الرسالة مطلوب");
            }
            
            // إرسال الرسالة حسب نوع API المستخدم
            $result = $this->sendViaAPI($phone_number, $message);
            
            if ($result['success']) {
                // حفظ السجل في قاعدة البيانات
                $this->logCommunication($client_id, $employee_id, 'whatsapp', null, $message, 'sent', $template_id);
                
                return [
                    'success' => true,
                    'message' => 'تم إرسال رسالة واتساب بنجاح',
                    'message_id' => $result['message_id'] ?? null
                ];
            } else {
                throw new Exception($result['message'] ?? 'فشل في إرسال الرسالة');
            }
            
        } catch (Exception $e) {
            Config::logError("WhatsApp send error: " . $e->getMessage());
            
            // حفظ سجل الفشل
            if ($client_id && $employee_id) {
                $this->logCommunication($client_id, $employee_id, 'whatsapp', null, $message, 'failed', $template_id, $e->getMessage());
            }
            
            return [
                'success' => false,
                'message' => 'فشل في إرسال رسالة واتساب: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * إرسال رسائل جماعية
     */
    public function sendBulkMessage($recipients, $message, $employee_id, $template_id = null) {
        $results = [
            'success' => 0,
            'failed' => 0,
            'errors' => []
        ];
        
        foreach ($recipients as $recipient) {
            $result = $this->sendMessage(
                $recipient['whatsapp_number'],
                $message,
                $recipient['client_id'] ?? null,
                $employee_id,
                $template_id
            );
            
            if ($result['success']) {
                $results['success']++;
            } else {
                $results['failed']++;
                $results['errors'][] = $recipient['name'] . ': ' . $result['message'];
            }
            
            // توقف قصير لتجنب الحظر
            sleep(2); // ثانيتان
        }
        
        return $results;
    }
    
    /**
     * إرسال الرسالة عبر API
     */
    private function sendViaAPI($phone_number, $message) {
        if (empty($this->api_url) || empty($this->api_token)) {
            // استخدام WhatsApp Web API (مثال)
            return $this->sendViaWebAPI($phone_number, $message);
        } else {
            // استخدام API مخصص (مثل Twilio أو WhatsApp Business API)
            return $this->sendViaCustomAPI($phone_number, $message);
        }
    }
    
    /**
     * إرسال عبر WhatsApp Web API
     */
    private function sendViaWebAPI($phone_number, $message) {
        // هذا مثال لاستخدام WhatsApp Web
        // في الواقع، ستحتاج إلى استخدام مكتبة أو خدمة خارجية
        
        $whatsapp_url = "https://wa.me/" . $phone_number . "?text=" . urlencode($message);
        
        return [
            'success' => true,
            'message' => 'تم إنشاء رابط واتساب',
            'url' => $whatsapp_url,
            'message_id' => 'web_' . time()
        ];
    }
    
    /**
     * إرسال عبر API مخصص
     */
    private function sendViaCustomAPI($phone_number, $message) {
        try {
            // مثال لاستخدام Twilio WhatsApp API
            $data = [
                'From' => 'whatsapp:+***********', // رقم Twilio
                'To' => 'whatsapp:' . $phone_number,
                'Body' => $message
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->api_url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $this->api_token,
                'Content-Type: application/x-www-form-urlencoded'
            ]);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code == 200 || $http_code == 201) {
                $response_data = json_decode($response, true);
                
                return [
                    'success' => true,
                    'message' => 'تم إرسال الرسالة بنجاح',
                    'message_id' => $response_data['sid'] ?? null
                ];
            } else {
                throw new Exception("HTTP Error: $http_code - $response");
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * تنسيق رقم الهاتف
     */
    private function formatPhoneNumber($phone) {
        // إزالة جميع الرموز غير الرقمية عدا +
        $phone = preg_replace('/[^0-9+]/', '', $phone);
        
        // تنسيق الأرقام الأردنية
        if (preg_match('/^(\+962|00962|962)([7-9][0-9]{8})$/', $phone, $matches)) {
            return '+962' . $matches[2];
        } elseif (preg_match('/^0([7-9][0-9]{8})$/', $phone, $matches)) {
            return '+962' . $matches[1];
        }
        
        // إذا كان الرقم يبدأ بـ + فهو منسق بالفعل
        if (strpos($phone, '+') === 0) {
            return $phone;
        }
        
        return $phone;
    }
    
    /**
     * التحقق من صحة رقم الهاتف
     */
    private function validatePhoneNumber($phone) {
        // التحقق من الأرقام الأردنية
        if (preg_match('/^\+962[7-9][0-9]{8}$/', $phone)) {
            return true;
        }
        
        // التحقق من الأرقام الدولية الأخرى
        if (preg_match('/^\+[1-9][0-9]{7,14}$/', $phone)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * تسجيل التواصل في قاعدة البيانات
     */
    private function logCommunication($client_id, $employee_id, $type, $subject, $message, $status, $template_id = null, $error_message = null) {
        try {
            $query = "INSERT INTO communication_log 
                     (client_id, employee_id, communication_type, subject, message, status, sent_at, created_at) 
                     VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())";
            
            $params = [$client_id, $employee_id, $type, $subject, $message, $status];
            
            $this->db->executeQuery($query, $params);
            
            // تسجيل الخطأ إذا وجد
            if ($error_message) {
                Config::logError("WhatsApp communication failed: $error_message");
            }
            
        } catch (Exception $e) {
            Config::logError("Log WhatsApp communication error: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على قوالب واتساب
     */
    public function getWhatsAppTemplates($user_id = null) {
        try {
            $query = "SELECT * FROM message_templates WHERE type = 'whatsapp' AND is_active = TRUE";
            $params = [];
            
            if ($user_id) {
                $query .= " AND (created_by = ? OR created_by IN (SELECT id FROM users WHERE role = 'admin'))";
                $params[] = $user_id;
            }
            
            $query .= " ORDER BY name";
            
            $stmt = $this->db->executeQuery($query, $params);
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            Config::logError("Get WhatsApp templates error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * معالجة المتغيرات في القالب
     */
    public function processTemplate($template_content, $variables) {
        $processed_content = $template_content;
        
        foreach ($variables as $key => $value) {
            $processed_content = str_replace('{{' . $key . '}}', $value, $processed_content);
        }
        
        return $processed_content;
    }
    
    /**
     * التحقق من حالة الرسالة
     */
    public function checkMessageStatus($message_id) {
        // هذه الوظيفة تعتمد على نوع API المستخدم
        // يمكن تطويرها حسب الحاجة
        
        return [
            'status' => 'unknown',
            'message' => 'لا يمكن التحقق من حالة الرسالة'
        ];
    }
    
    /**
     * التحقق من إعدادات واتساب
     */
    public function testWhatsAppSettings() {
        try {
            if (empty($this->api_url) && empty($this->api_token)) {
                return [
                    'success' => true,
                    'message' => 'سيتم استخدام WhatsApp Web (يتطلب تدخل يدوي)'
                ];
            }
            
            // اختبار الاتصال بـ API
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->api_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $this->api_token
            ]);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code == 200 || $http_code == 401) { // 401 يعني أن API يعمل لكن التوكن خاطئ
                return [
                    'success' => true,
                    'message' => 'إعدادات واتساب صحيحة'
                ];
            } else {
                throw new Exception("HTTP Error: $http_code");
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في إعدادات واتساب: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * إنشاء رابط واتساب مباشر
     */
    public function createWhatsAppLink($phone_number, $message = '') {
        $phone_number = $this->formatPhoneNumber($phone_number);
        $phone_number = str_replace('+', '', $phone_number); // إزالة + للرابط
        
        $url = "https://wa.me/" . $phone_number;
        
        if (!empty($message)) {
            $url .= "?text=" . urlencode($message);
        }
        
        return $url;
    }
}
?>
