    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6 text-md-start">
                    <small>&copy; <?php echo date('Y'); ?> <?php echo Config::COMPANY_NAME; ?>. جميع الحقوق محفوظة.</small>
                </div>
                <div class="col-md-6 text-md-end">
                    <small>
                        نظام التواصل الذكي - الإصدار 1.0
                        <span class="mx-2">|</span>
                        <a href="help.php" class="text-decoration-none">المساعدة</a>
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // إعدادات عامة
        const APP_CONFIG = {
            baseUrl: '<?php echo $_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']); ?>',
            csrfToken: '<?php echo generateCSRFToken(); ?>',
            userId: <?php echo $_SESSION['user_id'] ?? 'null'; ?>,
            userRole: '<?php echo $_SESSION['user_role'] ?? ''; ?>'
        };
        
        // وظائف مساعدة
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.container-fluid') || document.body;
            container.insertBefore(alertDiv, container.firstChild);
            
            // إزالة التنبيه تلقائياً بعد 5 ثوان
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
        
        // تأكيد الحذف
        function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
            return confirm(message);
        }
        
        // تنسيق التاريخ
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
        
        // تحديث الوقت الحالي
        function updateCurrentTime() {
            const timeElements = document.querySelectorAll('.current-time');
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA');
            
            timeElements.forEach(element => {
                element.textContent = timeString;
            });
        }
        
        // تحديث الوقت كل ثانية
        setInterval(updateCurrentTime, 1000);
        
        // تحميل البيانات بـ AJAX
        function loadData(url, callback) {
            fetch(url, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-Token': APP_CONFIG.csrfToken
                }
            })
            .then(response => response.json())
            .then(data => callback(data))
            .catch(error => {
                console.error('Error:', error);
                showAlert('حدث خطأ في تحميل البيانات', 'danger');
            });
        }
        
        // إرسال البيانات بـ AJAX
        function sendData(url, data, callback) {
            const formData = new FormData();
            
            // إضافة رمز CSRF
            formData.append('csrf_token', APP_CONFIG.csrfToken);
            
            // إضافة البيانات
            for (const key in data) {
                formData.append(key, data[key]);
            }
            
            fetch(url, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => callback(data))
            .catch(error => {
                console.error('Error:', error);
                showAlert('حدث خطأ في إرسال البيانات', 'danger');
            });
        }
        
        // تهيئة الجداول القابلة للفرز
        function initSortableTables() {
            const tables = document.querySelectorAll('.sortable-table');
            tables.forEach(table => {
                const headers = table.querySelectorAll('th[data-sort]');
                headers.forEach(header => {
                    header.style.cursor = 'pointer';
                    header.addEventListener('click', () => sortTable(table, header.dataset.sort));
                });
            });
        }
        
        // فرز الجدول
        function sortTable(table, column) {
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const isAscending = table.dataset.sortOrder !== 'asc';
            
            rows.sort((a, b) => {
                const aValue = a.querySelector(`[data-sort="${column}"]`)?.textContent || '';
                const bValue = b.querySelector(`[data-sort="${column}"]`)?.textContent || '';
                
                if (isAscending) {
                    return aValue.localeCompare(bValue, 'ar');
                } else {
                    return bValue.localeCompare(aValue, 'ar');
                }
            });
            
            // إعادة ترتيب الصفوف
            rows.forEach(row => tbody.appendChild(row));
            
            // تحديث اتجاه الفرز
            table.dataset.sortOrder = isAscending ? 'asc' : 'desc';
            
            // تحديث أيقونة الفرز
            const headers = table.querySelectorAll('th[data-sort]');
            headers.forEach(h => h.classList.remove('sort-asc', 'sort-desc'));
            
            const currentHeader = table.querySelector(`th[data-sort="${column}"]`);
            currentHeader.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
        }
        
        // تهيئة النماذج
        function initForms() {
            const forms = document.querySelectorAll('form[data-ajax="true"]');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const formData = new FormData(form);
                    const url = form.action || window.location.href;
                    
                    fetch(url, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showAlert(data.message, 'success');
                            if (data.redirect) {
                                setTimeout(() => {
                                    window.location.href = data.redirect;
                                }, 1500);
                            }
                        } else {
                            showAlert(data.message, 'danger');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showAlert('حدث خطأ في إرسال النموذج', 'danger');
                    });
                });
            });
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة التلميحات
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            
            // تهيئة النوافذ المنبثقة
            const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
            
            // تهيئة الجداول والنماذج
            initSortableTables();
            initForms();
            
            // تحديث الوقت الحالي
            updateCurrentTime();
        });
        
        // رسوم بيانية للوحة التحكم
        <?php if (isset($communicationData) || isset($sectorData)): ?>
        document.addEventListener('DOMContentLoaded', function() {
            // رسم بياني للتواصل
            <?php if (isset($communicationData)): ?>
            const commCtx = document.getElementById('communicationChart');
            if (commCtx) {
                new Chart(commCtx, {
                    type: 'line',
                    data: {
                        labels: ['البريد الإلكتروني', 'واتساب', 'الهاتف'],
                        datasets: [{
                            label: 'عدد الرسائل',
                            data: [communicationData.email, communicationData.whatsapp, 0],
                            borderColor: 'rgb(102, 126, 234)',
                            backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
            <?php endif; ?>
            
            // رسم بياني للقطاعات
            <?php if (isset($sectorData)): ?>
            const sectorCtx = document.getElementById('sectorChart');
            if (sectorCtx) {
                new Chart(sectorCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['تعليمي', 'حكومي', 'خاص'],
                        datasets: [{
                            data: [sectorData.educational, sectorData.government, sectorData.private],
                            backgroundColor: [
                                'rgb(102, 126, 234)',
                                'rgb(40, 167, 69)',
                                'rgb(23, 162, 184)'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }
            <?php endif; ?>
        });
        <?php endif; ?>
    </script>
</body>
</html>
