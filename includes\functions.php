<?php
/**
 * الوظائف المساعدة - نظام التواصل الذكي
 * Helper Functions - Smart Communication System
 */

require_once 'config/database.php';

/**
 * بدء الجلسة بشكل آمن
 * Start secure session
 */
function startSecureSession() {
    if (session_status() == PHP_SESSION_NONE) {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', 1);
        ini_set('session.use_strict_mode', 1);
        session_start();
    }
}

/**
 * التحقق من تسجيل الدخول
 * Check if user is logged in
 */
function isLoggedIn() {
    startSecureSession();
    return isset($_SESSION['user_id']) && isset($_SESSION['user_role']);
}

/**
 * التحقق من الصلاحيات
 * Check user permissions
 */
function hasPermission($required_role) {
    if (!isLoggedIn()) {
        return false;
    }
    
    $user_role = $_SESSION['user_role'];
    $roles_hierarchy = ['admin' => 3, 'supervisor' => 2, 'employee' => 1];
    
    return $roles_hierarchy[$user_role] >= $roles_hierarchy[$required_role];
}

/**
 * إعادة توجيه المستخدم
 * Redirect user
 */
function redirect($url) {
    header("Location: $url");
    exit();
}

/**
 * عرض رسالة تنبيه
 * Display alert message
 */
function showAlert($message, $type = 'info') {
    startSecureSession();
    $_SESSION['alert'] = [
        'message' => $message,
        'type' => $type
    ];
}

/**
 * الحصول على رسالة التنبيه وحذفها
 * Get and clear alert message
 */
function getAlert() {
    startSecureSession();
    if (isset($_SESSION['alert'])) {
        $alert = $_SESSION['alert'];
        unset($_SESSION['alert']);
        return $alert;
    }
    return null;
}

/**
 * تنسيق التاريخ والوقت
 * Format date and time
 */
function formatDateTime($datetime, $format = 'Y-m-d H:i:s') {
    if (empty($datetime)) return '';
    
    $date = new DateTime($datetime);
    $date->setTimezone(new DateTimeZone(Config::TIMEZONE));
    
    return $date->format($format);
}

/**
 * تنسيق التاريخ بالعربية
 * Format date in Arabic
 */
function formatDateArabic($datetime) {
    if (empty($datetime)) return '';
    
    $date = new DateTime($datetime);
    $date->setTimezone(new DateTimeZone(Config::TIMEZONE));
    
    $months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $day = $date->format('d');
    $month = $months[(int)$date->format('m')];
    $year = $date->format('Y');
    $time = $date->format('H:i');
    
    return "$day $month $year - $time";
}

/**
 * التحقق من صحة البريد الإلكتروني
 * Validate email address
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * التحقق من صحة رقم الهاتف
 * Validate phone number
 */
function validatePhone($phone) {
    // تنسيق أرقام الهاتف الأردنية
    $pattern = '/^(\+962|00962|962|0)?[7-9][0-9]{8}$/';
    return preg_match($pattern, $phone);
}

/**
 * تنسيق رقم الهاتف
 * Format phone number
 */
function formatPhone($phone) {
    $phone = preg_replace('/[^0-9+]/', '', $phone);
    
    if (preg_match('/^(\+962|00962|962)([7-9][0-9]{8})$/', $phone, $matches)) {
        return '+962' . $matches[2];
    } elseif (preg_match('/^0([7-9][0-9]{8})$/', $phone, $matches)) {
        return '+962' . $matches[1];
    }
    
    return $phone;
}

/**
 * إنشاء كلمة مرور عشوائية
 * Generate random password
 */
function generatePassword($length = 12) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    return substr(str_shuffle($chars), 0, $length);
}

/**
 * تشفير كلمة المرور
 * Hash password
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * التحقق من كلمة المرور
 * Verify password
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * إنشاء رمز CSRF
 * Generate CSRF token
 */
function generateCSRFToken() {
    startSecureSession();
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * التحقق من رمز CSRF
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    startSecureSession();
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * تسجيل نشاط المستخدم
 * Log user activity
 */
function logUserActivity($user_id, $action, $details = '') {
    try {
        $db = new Database();
        $query = "INSERT INTO user_activity_log (user_id, action, details, ip_address, user_agent, created_at) 
                  VALUES (?, ?, ?, ?, ?, NOW())";
        
        $params = [
            $user_id,
            $action,
            $details,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        $db->executeQuery($query, $params);
        
    } catch (Exception $e) {
        Config::logError("Failed to log user activity: " . $e->getMessage());
    }
}

/**
 * الحصول على إعداد النظام
 * Get system setting
 */
function getSystemSetting($key, $default = '') {
    try {
        $db = new Database();
        $query = "SELECT setting_value FROM system_settings WHERE setting_key = ?";
        $stmt = $db->executeQuery($query, [$key]);
        
        $result = $stmt->fetch();
        return $result ? $result['setting_value'] : $default;
        
    } catch (Exception $e) {
        Config::logError("Failed to get system setting: " . $e->getMessage());
        return $default;
    }
}

/**
 * تحديث إعداد النظام
 * Update system setting
 */
function updateSystemSetting($key, $value) {
    try {
        $db = new Database();
        $query = "INSERT INTO system_settings (setting_key, setting_value) 
                  VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?";
        
        $db->executeQuery($query, [$key, $value, $value]);
        return true;
        
    } catch (Exception $e) {
        Config::logError("Failed to update system setting: " . $e->getMessage());
        return false;
    }
}

/**
 * تحويل النص إلى UTF-8
 * Convert text to UTF-8
 */
function toUTF8($text) {
    if (mb_check_encoding($text, 'UTF-8')) {
        return $text;
    }
    return mb_convert_encoding($text, 'UTF-8', 'auto');
}

/**
 * اقتطاع النص
 * Truncate text
 */
function truncateText($text, $length = 100, $suffix = '...') {
    if (mb_strlen($text) <= $length) {
        return $text;
    }
    return mb_substr($text, 0, $length) . $suffix;
}

/**
 * تنظيف اسم الملف
 * Sanitize filename
 */
function sanitizeFilename($filename) {
    $filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);
    return trim($filename, '._-');
}
?>
