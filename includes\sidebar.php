<?php
// تحديد الصفحة الحالية
$current_page = basename($_SERVER['PHP_SELF']);
?>

<nav class="col-md-3 col-lg-2 d-md-block sidebar" id="sidebar">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <!-- لوحة التحكم -->
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'index.php') ? 'active' : ''; ?>" href="index.php">
                    <i class="fas fa-tachometer-alt"></i>
                    لوحة التحكم
                </a>
            </li>
            
            <!-- إدارة العملاء -->
            <li class="nav-item">
                <a class="nav-link <?php echo (strpos($current_page, 'clients') !== false) ? 'active' : ''; ?>" 
                   href="#clientsSubmenu" data-bs-toggle="collapse" role="button" 
                   aria-expanded="<?php echo (strpos($current_page, 'clients') !== false) ? 'true' : 'false'; ?>">
                    <i class="fas fa-users"></i>
                    إدارة العملاء
                    <i class="fas fa-chevron-down ms-auto"></i>
                </a>
                <div class="collapse <?php echo (strpos($current_page, 'clients') !== false) ? 'show' : ''; ?>" id="clientsSubmenu">
                    <ul class="nav flex-column ms-3">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'clients.php') ? 'active' : ''; ?>" href="clients.php">
                                <i class="fas fa-list"></i>
                                قائمة العملاء
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'add-client.php') ? 'active' : ''; ?>" href="add-client.php">
                                <i class="fas fa-plus"></i>
                                إضافة عميل جديد
                            </a>
                        </li>
                        <?php if (hasPermission('supervisor')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'client-assignments.php') ? 'active' : ''; ?>" href="client-assignments.php">
                                <i class="fas fa-user-tag"></i>
                                تعيين العملاء
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </li>
            
            <!-- التواصل -->
            <li class="nav-item">
                <a class="nav-link <?php echo (strpos($current_page, 'communication') !== false || strpos($current_page, 'messages') !== false) ? 'active' : ''; ?>" 
                   href="#communicationSubmenu" data-bs-toggle="collapse" role="button" 
                   aria-expanded="<?php echo (strpos($current_page, 'communication') !== false || strpos($current_page, 'messages') !== false) ? 'true' : 'false'; ?>">
                    <i class="fas fa-comments"></i>
                    التواصل
                    <i class="fas fa-chevron-down ms-auto"></i>
                </a>
                <div class="collapse <?php echo (strpos($current_page, 'communication') !== false || strpos($current_page, 'messages') !== false) ? 'show' : ''; ?>" id="communicationSubmenu">
                    <ul class="nav flex-column ms-3">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'send-email.php') ? 'active' : ''; ?>" href="send-email.php">
                                <i class="fas fa-envelope"></i>
                                إرسال بريد إلكتروني
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'send-whatsapp.php') ? 'active' : ''; ?>" href="send-whatsapp.php">
                                <i class="fab fa-whatsapp"></i>
                                إرسال واتساب
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'communication-log.php') ? 'active' : ''; ?>" href="communication-log.php">
                                <i class="fas fa-history"></i>
                                سجل التواصل
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'message-templates.php') ? 'active' : ''; ?>" href="message-templates.php">
                                <i class="fas fa-file-alt"></i>
                                قوالب الرسائل
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            
            <!-- التقارير -->
            <li class="nav-item">
                <a class="nav-link <?php echo (strpos($current_page, 'reports') !== false) ? 'active' : ''; ?>" 
                   href="#reportsSubmenu" data-bs-toggle="collapse" role="button" 
                   aria-expanded="<?php echo (strpos($current_page, 'reports') !== false) ? 'true' : 'false'; ?>">
                    <i class="fas fa-chart-bar"></i>
                    التقارير
                    <i class="fas fa-chevron-down ms-auto"></i>
                </a>
                <div class="collapse <?php echo (strpos($current_page, 'reports') !== false) ? 'show' : ''; ?>" id="reportsSubmenu">
                    <ul class="nav flex-column ms-3">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'communication-reports.php') ? 'active' : ''; ?>" href="communication-reports.php">
                                <i class="fas fa-chart-line"></i>
                                تقارير التواصل
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'client-reports.php') ? 'active' : ''; ?>" href="client-reports.php">
                                <i class="fas fa-chart-pie"></i>
                                تقارير العملاء
                            </a>
                        </li>
                        <?php if (hasPermission('supervisor')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'employee-reports.php') ? 'active' : ''; ?>" href="employee-reports.php">
                                <i class="fas fa-chart-area"></i>
                                تقارير الموظفين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'client-assignments.php') ? 'active' : ''; ?>" href="client-assignments.php">
                                <i class="fas fa-user-plus"></i>
                                تعيين العملاء
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </li>
            
            <!-- الإشعارات -->
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'notifications.php') ? 'active' : ''; ?>" href="notifications.php">
                    <i class="fas fa-bell"></i>
                    الإشعارات
                    <?php if (isset($notifications_stats['unread_count']) && $notifications_stats['unread_count'] > 0): ?>
                    <span class="badge bg-danger rounded-pill ms-auto"><?php echo $notifications_stats['unread_count']; ?></span>
                    <?php endif; ?>
                </a>
            </li>
            
            <?php if (hasPermission('admin')): ?>
            <!-- الإدارة -->
            <li class="nav-item mt-3">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>الإدارة</span>
                </h6>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo (strpos($current_page, 'users') !== false) ? 'active' : ''; ?>" 
                   href="#usersSubmenu" data-bs-toggle="collapse" role="button" 
                   aria-expanded="<?php echo (strpos($current_page, 'users') !== false) ? 'true' : 'false'; ?>">
                    <i class="fas fa-user-cog"></i>
                    إدارة المستخدمين
                    <i class="fas fa-chevron-down ms-auto"></i>
                </a>
                <div class="collapse <?php echo (strpos($current_page, 'users') !== false) ? 'show' : ''; ?>" id="usersSubmenu">
                    <ul class="nav flex-column ms-3">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'users.php') ? 'active' : ''; ?>" href="users.php">
                                <i class="fas fa-list"></i>
                                قائمة المستخدمين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'add-user.php') ? 'active' : ''; ?>" href="add-user.php">
                                <i class="fas fa-user-plus"></i>
                                إضافة مستخدم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'user-sessions.php') ? 'active' : ''; ?>" href="user-sessions.php">
                                <i class="fas fa-clock"></i>
                                جلسات المستخدمين
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'system-settings.php') ? 'active' : ''; ?>" href="system-settings.php">
                    <i class="fas fa-cogs"></i>
                    إعدادات النظام
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'email-settings.php') ? 'active' : ''; ?>" href="email-settings.php">
                    <i class="fas fa-envelope-open-text"></i>
                    إعدادات البريد
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'email-simulation-log.php') ? 'active' : ''; ?>" href="email-simulation-log.php">
                    <i class="fas fa-file-alt"></i>
                    سجل محاكاة البريد
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'system-logs.php') ? 'active' : ''; ?>" href="system-logs.php">
                    <i class="fas fa-file-alt"></i>
                    سجلات النظام
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'backup.php') ? 'active' : ''; ?>" href="backup.php">
                    <i class="fas fa-database"></i>
                    النسخ الاحتياطي
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'test-system.php') ? 'active' : ''; ?>" href="test-system.php">
                    <i class="fas fa-vial"></i>
                    اختبار النظام
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'test-reports.php') ? 'active' : ''; ?>" href="test-reports.php">
                    <i class="fas fa-chart-line"></i>
                    اختبار التقارير
                </a>
            </li>
            <?php endif; ?>
            
            <!-- الملف الشخصي والإعدادات -->
            <li class="nav-item mt-3">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>الحساب</span>
                </h6>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'profile.php') ? 'active' : ''; ?>" href="profile.php">
                    <i class="fas fa-user"></i>
                    الملف الشخصي
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'change-password.php') ? 'active' : ''; ?>" href="change-password.php">
                    <i class="fas fa-key"></i>
                    تغيير كلمة المرور
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
            </li>
        </ul>
        
        <!-- معلومات المستخدم -->
        <div class="mt-4 p-3 bg-light rounded mx-3">
            <div class="d-flex align-items-center">
                <div class="avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                    <i class="fas fa-user"></i>
                </div>
                <div class="ms-3">
                    <div class="fw-bold"><?php echo htmlspecialchars($_SESSION['user_name'] ?? ''); ?></div>
                    <small class="text-muted">
                        <?php 
                        $roles = [
                            'admin' => 'مدير النظام',
                            'supervisor' => 'مشرف',
                            'employee' => 'موظف'
                        ];
                        echo $roles[$_SESSION['user_role']] ?? $_SESSION['user_role'];
                        ?>
                    </small>
                </div>
            </div>
        </div>
    </div>
</nav>

<script>
// تبديل الشريط الجانبي في الجوال
document.addEventListener('DOMContentLoaded', function() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const sidebarOverlay = document.getElementById('sidebarOverlay');
    
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
            sidebarOverlay.classList.toggle('show');
        });
    }
    
    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', function() {
            sidebar.classList.remove('show');
            sidebarOverlay.classList.remove('show');
        });
    }
    
    // إغلاق الشريط الجانبي عند النقر على رابط في الجوال
    const sidebarLinks = sidebar.querySelectorAll('.nav-link');
    sidebarLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (window.innerWidth < 768) {
                sidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
            }
        });
    });
});
</script>
