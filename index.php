<?php
/**
 * الصفحة الرئيسية - نظام التواصل الذكي
 * Main Page - Smart Communication System
 */

require_once 'includes/functions.php';

// بدء الجلسة
startSecureSession();

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('login.php');
}

// الحصول على بيانات المستخدم
$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['user_name'];
$user_role = $_SESSION['user_role'];

// الحصول على الإحصائيات
try {
    $db = new Database();
    
    // إحصائيات العملاء
    $clients_query = "SELECT 
        COUNT(*) as total_clients,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_clients,
        COUNT(CASE WHEN sector = 'educational' THEN 1 END) as educational_clients,
        COUNT(CASE WHEN sector = 'government' THEN 1 END) as government_clients,
        COUNT(CASE WHEN sector = 'private' THEN 1 END) as private_clients
        FROM clients";
    
    if ($user_role !== 'admin') {
        $clients_query .= " WHERE assigned_employee_id = ?";
        $clients_stmt = $db->executeQuery($clients_query, [$user_id]);
    } else {
        $clients_stmt = $db->executeQuery($clients_query);
    }
    
    $clients_stats = $clients_stmt->fetch();
    
    // إحصائيات التواصل
    $comm_query = "SELECT 
        COUNT(*) as total_communications,
        COUNT(CASE WHEN communication_type = 'email' THEN 1 END) as email_count,
        COUNT(CASE WHEN communication_type = 'whatsapp' THEN 1 END) as whatsapp_count,
        COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_count,
        COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as week_count
        FROM communication_log";
    
    if ($user_role !== 'admin') {
        $comm_query .= " WHERE employee_id = ?";
        $comm_stmt = $db->executeQuery($comm_query, [$user_id]);
    } else {
        $comm_stmt = $db->executeQuery($comm_query);
    }
    
    $comm_stats = $comm_stmt->fetch();
    
    // الإشعارات غير المقروءة
    $notifications_query = "SELECT COUNT(*) as unread_count FROM notifications WHERE user_id = ? AND is_read = FALSE";
    $notifications_stmt = $db->executeQuery($notifications_query, [$user_id]);
    $notifications_stats = $notifications_stmt->fetch();
    
    // آخر التواصلات
    $recent_query = "SELECT cl.*, c.name as client_name, c.organization, u.full_name as employee_name
                     FROM communication_log cl
                     JOIN clients c ON cl.client_id = c.id
                     JOIN users u ON cl.employee_id = u.id";
    
    if ($user_role !== 'admin') {
        $recent_query .= " WHERE cl.employee_id = ?";
        $recent_query .= " ORDER BY cl.created_at DESC LIMIT 10";
        $recent_stmt = $db->executeQuery($recent_query, [$user_id]);
    } else {
        $recent_query .= " ORDER BY cl.created_at DESC LIMIT 10";
        $recent_stmt = $db->executeQuery($recent_query);
    }
    
    $recent_communications = $recent_stmt->fetchAll();
    
} catch (Exception $e) {
    Config::logError("Dashboard error: " . $e->getMessage());
    $clients_stats = $comm_stats = $notifications_stats = [];
    $recent_communications = [];
}

$page_title = "لوحة التحكم";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- الشريط الجانبي -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- المحتوى الرئيسي -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">مرحباً، <?php echo htmlspecialchars($user_name); ?></h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary">تصدير</button>
                    </div>
                </div>
            </div>

            <!-- بطاقات الإحصائيات -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        إجمالي العملاء
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($clients_stats['total_clients'] ?? 0); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        العملاء النشطين
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($clients_stats['active_clients'] ?? 0); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-check fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        رسائل اليوم
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($comm_stats['today_count'] ?? 0); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-envelope fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-right-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        إشعارات جديدة
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($notifications_stats['unread_count'] ?? 0); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-bell fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الرسوم البيانية والجداول -->
            <div class="row">
                <!-- رسم بياني للتواصل -->
                <div class="col-xl-8 col-lg-7">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">إحصائيات التواصل</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-area">
                                <canvas id="communicationChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- توزيع العملاء حسب القطاع -->
                <div class="col-xl-4 col-lg-5">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">توزيع العملاء</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-pie pt-4 pb-2">
                                <canvas id="sectorChart"></canvas>
                            </div>
                            <div class="mt-4 text-center small">
                                <span class="mr-2">
                                    <i class="fas fa-circle text-primary"></i> تعليمي
                                </span>
                                <span class="mr-2">
                                    <i class="fas fa-circle text-success"></i> حكومي
                                </span>
                                <span class="mr-2">
                                    <i class="fas fa-circle text-info"></i> خاص
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- آخر التواصلات -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">آخر التواصلات</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>العميل</th>
                                    <th>المؤسسة</th>
                                    <th>نوع التواصل</th>
                                    <th>الموضوع</th>
                                    <th>الموظف</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($recent_communications)): ?>
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد تواصلات حديثة</td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($recent_communications as $comm): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($comm['client_name']); ?></td>
                                    <td><?php echo htmlspecialchars($comm['organization']); ?></td>
                                    <td>
                                        <?php if ($comm['communication_type'] == 'email'): ?>
                                            <i class="fas fa-envelope text-primary"></i> بريد إلكتروني
                                        <?php elseif ($comm['communication_type'] == 'whatsapp'): ?>
                                            <i class="fab fa-whatsapp text-success"></i> واتساب
                                        <?php else: ?>
                                            <i class="fas fa-phone text-info"></i> هاتف
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo truncateText(htmlspecialchars($comm['subject'] ?? ''), 50); ?></td>
                                    <td><?php echo htmlspecialchars($comm['employee_name']); ?></td>
                                    <td><?php echo formatDateArabic($comm['created_at']); ?></td>
                                    <td>
                                        <?php
                                        $status_class = [
                                            'sent' => 'badge-primary',
                                            'delivered' => 'badge-info',
                                            'read' => 'badge-success',
                                            'replied' => 'badge-success',
                                            'failed' => 'badge-danger'
                                        ];
                                        $status_text = [
                                            'sent' => 'مرسل',
                                            'delivered' => 'تم التسليم',
                                            'read' => 'مقروء',
                                            'replied' => 'تم الرد',
                                            'failed' => 'فشل'
                                        ];
                                        ?>
                                        <span class="badge <?php echo $status_class[$comm['status']] ?? 'badge-secondary'; ?>">
                                            <?php echo $status_text[$comm['status']] ?? $comm['status']; ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// بيانات الرسوم البيانية
const communicationData = {
    email: <?php echo $comm_stats['email_count'] ?? 0; ?>,
    whatsapp: <?php echo $comm_stats['whatsapp_count'] ?? 0; ?>,
    total: <?php echo $comm_stats['total_communications'] ?? 0; ?>
};

const sectorData = {
    educational: <?php echo $clients_stats['educational_clients'] ?? 0; ?>,
    government: <?php echo $clients_stats['government_clients'] ?? 0; ?>,
    private: <?php echo $clients_stats['private_clients'] ?? 0; ?>
};
</script>

<?php include 'includes/footer.php'; ?>
