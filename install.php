<?php
/**
 * ملف التثبيت التلقائي - نظام التواصل الذكي
 * Auto Installation File - Smart Communication System
 */

// التحقق من وجود ملف التكوين
if (file_exists('config/installed.lock')) {
    die('النظام مثبت بالفعل. إذا كنت تريد إعادة التثبيت، احذف ملف config/installed.lock');
}

$step = (int)($_GET['step'] ?? 1);
$errors = [];
$success_messages = [];

// معالجة خطوات التثبيت
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    switch ($step) {
        case 2:
            // التحقق من المتطلبات
            $step = 3;
            break;
            
        case 3:
            // إعداد قاعدة البيانات
            $db_host = $_POST['db_host'] ?? 'localhost';
            $db_name = $_POST['db_name'] ?? 'smart_communication';
            $db_user = $_POST['db_user'] ?? 'root';
            $db_pass = $_POST['db_pass'] ?? '';
            
            try {
                // اختبار الاتصال
                $pdo = new PDO("mysql:host=$db_host", $db_user, $db_pass);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // إنشاء قاعدة البيانات
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $pdo->exec("USE `$db_name`");
                
                // تنفيذ SQL
                $sql = file_get_contents('database/smart_communication.sql');
                $pdo->exec($sql);
                
                // حفظ إعدادات قاعدة البيانات
                $config_content = file_get_contents('config/database.php');
                $config_content = str_replace("'localhost'", "'$db_host'", $config_content);
                $config_content = str_replace("'smart_communication'", "'$db_name'", $config_content);
                $config_content = str_replace("'root'", "'$db_user'", $config_content);
                $config_content = str_replace("''", "'$db_pass'", $config_content);
                
                file_put_contents('config/database.php', $config_content);
                
                $success_messages[] = 'تم إنشاء قاعدة البيانات بنجاح';
                $step = 4;
                
            } catch (Exception $e) {
                $errors[] = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
            }
            break;
            
        case 4:
            // إعداد المدير
            $admin_name = $_POST['admin_name'] ?? '';
            $admin_email = $_POST['admin_email'] ?? '';
            $admin_password = $_POST['admin_password'] ?? '';
            $company_name = $_POST['company_name'] ?? '';
            
            if (empty($admin_name) || empty($admin_email) || empty($admin_password)) {
                $errors[] = 'جميع الحقول مطلوبة';
            } else {
                try {
                    require_once 'config/database.php';
                    $db = new Database();
                    
                    // تحديث بيانات المدير
                    $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
                    $query = "UPDATE users SET full_name = ?, email = ?, password = ? WHERE username = 'admin'";
                    $db->executeQuery($query, [$admin_name, $admin_email, $hashed_password]);
                    
                    // تحديث اسم الشركة
                    $query = "UPDATE system_settings SET setting_value = ? WHERE setting_key = 'company_name'";
                    $db->executeQuery($query, [$company_name]);
                    
                    $success_messages[] = 'تم إعداد حساب المدير بنجاح';
                    $step = 5;
                    
                } catch (Exception $e) {
                    $errors[] = 'خطأ في إعداد المدير: ' . $e->getMessage();
                }
            }
            break;
            
        case 5:
            // إنهاء التثبيت
            file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
            $step = 6;
            break;
    }
}

// فحص المتطلبات
function checkRequirements() {
    $requirements = [
        'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'PDO Extension' => extension_loaded('pdo'),
        'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
        'cURL Extension' => extension_loaded('curl'),
        'OpenSSL Extension' => extension_loaded('openssl'),
        'Config Directory Writable' => is_writable('config/'),
        'Logs Directory Writable' => is_writable('logs/') || mkdir('logs/', 0755, true),
    ];
    
    return $requirements;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام التواصل الذكي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .install-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .install-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
        }
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .install-body {
            padding: 2rem;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
        }
        .step.active {
            background: #667eea;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
        .requirement-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-card">
            <div class="install-header">
                <h2><i class="fas fa-cog me-2"></i>تثبيت نظام التواصل الذكي</h2>
                <p class="mb-0">مرحباً بك في معالج التثبيت</p>
            </div>
            
            <div class="install-body">
                <!-- مؤشر الخطوات -->
                <div class="step-indicator">
                    <?php for ($i = 1; $i <= 6; $i++): ?>
                    <div class="step <?php echo $i < $step ? 'completed' : ($i == $step ? 'active' : ''); ?>">
                        <?php echo $i; ?>
                    </div>
                    <?php endfor; ?>
                </div>
                
                <!-- عرض الأخطاء -->
                <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                
                <!-- عرض رسائل النجاح -->
                <?php if (!empty($success_messages)): ?>
                <div class="alert alert-success">
                    <ul class="mb-0">
                        <?php foreach ($success_messages as $message): ?>
                        <li><?php echo htmlspecialchars($message); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                
                <?php if ($step == 1): ?>
                <!-- الخطوة 1: الترحيب -->
                <div class="text-center">
                    <h4>مرحباً بك في نظام التواصل الذكي</h4>
                    <p class="text-muted">سيقوم هذا المعالج بإرشادك خلال عملية التثبيت</p>
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-1"></i>قبل البدء:</h6>
                        <ul class="text-start mb-0">
                            <li>تأكد من تشغيل Apache و MySQL</li>
                            <li>تأكد من وجود صلاحيات الكتابة في مجلد المشروع</li>
                            <li>احضر بيانات قاعدة البيانات</li>
                        </ul>
                    </div>
                    <form method="POST" action="?step=2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-1"></i>
                            البدء في التثبيت
                        </button>
                    </form>
                </div>
                
                <?php elseif ($step == 2): ?>
                <!-- الخطوة 2: فحص المتطلبات -->
                <h4>فحص متطلبات النظام</h4>
                <?php 
                $requirements = checkRequirements();
                $all_passed = true;
                ?>
                <?php foreach ($requirements as $requirement => $passed): ?>
                <div class="requirement-item">
                    <span><?php echo $requirement; ?></span>
                    <span class="badge <?php echo $passed ? 'bg-success' : 'bg-danger'; ?>">
                        <i class="fas <?php echo $passed ? 'fa-check' : 'fa-times'; ?>"></i>
                        <?php echo $passed ? 'مطابق' : 'غير مطابق'; ?>
                    </span>
                </div>
                <?php 
                if (!$passed) $all_passed = false;
                endforeach; 
                ?>
                
                <div class="mt-3">
                    <?php if ($all_passed): ?>
                    <form method="POST" action="?step=3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-1"></i>
                            متابعة
                        </button>
                    </form>
                    <?php else: ?>
                    <div class="alert alert-warning">
                        يرجى حل المشاكل أعلاه قبل المتابعة
                    </div>
                    <a href="?step=2" class="btn btn-secondary">
                        <i class="fas fa-refresh me-1"></i>
                        إعادة الفحص
                    </a>
                    <?php endif; ?>
                </div>
                
                <?php elseif ($step == 3): ?>
                <!-- الخطوة 3: إعداد قاعدة البيانات -->
                <h4>إعداد قاعدة البيانات</h4>
                <form method="POST" action="?step=3">
                    <div class="mb-3">
                        <label for="db_host" class="form-label">خادم قاعدة البيانات</label>
                        <input type="text" class="form-control" id="db_host" name="db_host" value="localhost" required>
                    </div>
                    <div class="mb-3">
                        <label for="db_name" class="form-label">اسم قاعدة البيانات</label>
                        <input type="text" class="form-control" id="db_name" name="db_name" value="smart_communication" required>
                    </div>
                    <div class="mb-3">
                        <label for="db_user" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="db_user" name="db_user" value="root" required>
                    </div>
                    <div class="mb-3">
                        <label for="db_pass" class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="db_pass" name="db_pass">
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-database me-1"></i>
                        إنشاء قاعدة البيانات
                    </button>
                </form>
                
                <?php elseif ($step == 4): ?>
                <!-- الخطوة 4: إعداد المدير -->
                <h4>إعداد حساب المدير</h4>
                <form method="POST" action="?step=4">
                    <div class="mb-3">
                        <label for="admin_name" class="form-label">اسم المدير</label>
                        <input type="text" class="form-control" id="admin_name" name="admin_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="admin_email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="admin_email" name="admin_email" required>
                    </div>
                    <div class="mb-3">
                        <label for="admin_password" class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="admin_password" name="admin_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="company_name" class="form-label">اسم الشركة</label>
                        <input type="text" class="form-control" id="company_name" name="company_name" value="شركتك" required>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-user-cog me-1"></i>
                        إنشاء حساب المدير
                    </button>
                </form>
                
                <?php elseif ($step == 5): ?>
                <!-- الخطوة 5: إنهاء التثبيت -->
                <div class="text-center">
                    <h4>إنهاء التثبيت</h4>
                    <p class="text-muted">تم إعداد النظام بنجاح. اضغط على "إنهاء" لحفظ إعدادات التثبيت.</p>
                    <form method="POST" action="?step=5">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-check me-1"></i>
                            إنهاء التثبيت
                        </button>
                    </form>
                </div>
                
                <?php elseif ($step == 6): ?>
                <!-- الخطوة 6: اكتمال التثبيت -->
                <div class="text-center">
                    <div class="mb-4">
                        <i class="fas fa-check-circle fa-5x text-success"></i>
                    </div>
                    <h4 class="text-success">تم التثبيت بنجاح!</h4>
                    <p class="text-muted">يمكنك الآن استخدام نظام التواصل الذكي</p>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-1"></i>معلومات مهمة:</h6>
                        <ul class="text-start mb-0">
                            <li>احذف ملف install.php لأسباب أمنية</li>
                            <li>قم بإعداد البريد الإلكتروني من إعدادات النظام</li>
                            <li>قم بإعداد واتساب API إذا كنت تريد الإرسال التلقائي</li>
                        </ul>
                    </div>
                    
                    <a href="index.php" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt me-1"></i>
                        دخول النظام
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
