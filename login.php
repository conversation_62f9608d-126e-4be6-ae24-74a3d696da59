<?php
/**
 * صفحة تسجيل الدخول - نظام التواصل الذكي
 * Login Page - Smart Communication System
 */

require_once 'includes/functions.php';

// بدء الجلسة
startSecureSession();

// إعادة توجيه المستخدم المسجل دخوله
if (isLoggedIn()) {
    redirect('index.php');
}

$error_message = '';
$login_attempts = 0;

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = Config::sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // التحقق من رمز CSRF
    if (!verifyCSRFToken($csrf_token)) {
        $error_message = 'خطأ في التحقق من الأمان';
    } else {
        // التحقق من محاولات تسجيل الدخول
        $ip_address = $_SERVER['REMOTE_ADDR'];
        $lockout_key = "login_attempts_$ip_address";
        
        if (isset($_SESSION[$lockout_key])) {
            $attempts_data = $_SESSION[$lockout_key];
            if ($attempts_data['count'] >= Config::MAX_LOGIN_ATTEMPTS) {
                $time_diff = time() - $attempts_data['last_attempt'];
                if ($time_diff < Config::LOCKOUT_TIME) {
                    $remaining_time = Config::LOCKOUT_TIME - $time_diff;
                    $error_message = "تم حظر تسجيل الدخول لمدة " . ceil($remaining_time / 60) . " دقيقة";
                } else {
                    unset($_SESSION[$lockout_key]);
                }
            }
        }
        
        if (empty($error_message)) {
            try {
                $db = new Database();
                
                // البحث عن المستخدم
                $query = "SELECT id, username, email, password, full_name, role, is_active 
                         FROM users 
                         WHERE (username = ? OR email = ?) AND is_active = TRUE";
                
                $stmt = $db->executeQuery($query, [$username, $username]);
                $user = $stmt->fetch();
                
                if ($user && verifyPassword($password, $user['password'])) {
                    // تسجيل دخول ناجح
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['user_name'] = $user['full_name'];
                    $_SESSION['user_role'] = $user['role'];
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['login_time'] = time();
                    
                    // إنشاء رمز الجلسة
                    $session_token = bin2hex(random_bytes(32));
                    $_SESSION['session_token'] = $session_token;
                    
                    // حفظ الجلسة في قاعدة البيانات
                    $session_query = "INSERT INTO user_sessions 
                                     (user_id, session_token, ip_address, user_agent, login_time) 
                                     VALUES (?, ?, ?, ?, NOW())";
                    
                    $db->executeQuery($session_query, [
                        $user['id'],
                        $session_token,
                        $_SERVER['REMOTE_ADDR'],
                        $_SERVER['HTTP_USER_AGENT'] ?? ''
                    ]);
                    
                    // تحديث آخر تسجيل دخول
                    $update_query = "UPDATE users SET last_login = NOW() WHERE id = ?";
                    $db->executeQuery($update_query, [$user['id']]);
                    
                    // تسجيل النشاط
                    logUserActivity($user['id'], 'login', 'تسجيل دخول ناجح');
                    
                    // حذف محاولات تسجيل الدخول الفاشلة
                    unset($_SESSION[$lockout_key]);
                    
                    // إعادة التوجيه
                    redirect('index.php');
                    
                } else {
                    // تسجيل دخول فاشل
                    $error_message = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                    
                    // تسجيل محاولة فاشلة
                    if (!isset($_SESSION[$lockout_key])) {
                        $_SESSION[$lockout_key] = ['count' => 0, 'last_attempt' => 0];
                    }
                    
                    $_SESSION[$lockout_key]['count']++;
                    $_SESSION[$lockout_key]['last_attempt'] = time();
                    
                    // تسجيل النشاط المشبوه
                    Config::logError("Failed login attempt for username: $username from IP: $ip_address");
                }
                
            } catch (Exception $e) {
                Config::logError("Login error: " . $e->getMessage());
                $error_message = 'حدث خطأ في النظام، يرجى المحاولة لاحقاً';
            }
        }
    }
}

// إنشاء رمز CSRF
$csrf_token = generateCSRFToken();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - <?php echo Config::SITE_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }
        
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-body {
            padding: 2rem;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .input-group-text {
            background: transparent;
            border: 2px solid #e9ecef;
            border-left: none;
            color: #6c757d;
        }
        
        .form-control:focus + .input-group-text {
            border-color: #667eea;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="login-card">
                        <div class="login-header">
                            <div class="logo">
                                <i class="fas fa-comments fa-2x"></i>
                            </div>
                            <h3 class="mb-0"><?php echo Config::SITE_NAME; ?></h3>
                            <p class="mb-0 mt-2">مرحباً بك، يرجى تسجيل الدخول</p>
                        </div>
                        
                        <div class="login-body">
                            <?php if (!empty($error_message)): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo htmlspecialchars($error_message); ?>
                            </div>
                            <?php endif; ?>
                            
                            <form method="POST" action="">
                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                
                                <div class="mb-3">
                                    <label for="username" class="form-label">اسم المستخدم أو البريد الإلكتروني</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="username" name="username" 
                                               value="<?php echo htmlspecialchars($username ?? ''); ?>" 
                                               required autocomplete="username">
                                        <span class="input-group-text">
                                            <i class="fas fa-user"></i>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="password" class="form-label">كلمة المرور</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="password" name="password" 
                                               required autocomplete="current-password">
                                        <span class="input-group-text">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-login">
                                        <i class="fas fa-sign-in-alt me-2"></i>
                                        تسجيل الدخول
                                    </button>
                                </div>
                            </form>
                            
                            <div class="text-center mt-4">
                                <small class="text-muted">
                                    هل نسيت كلمة المرور؟ 
                                    <a href="forgot-password.php" class="text-decoration-none">اضغط هنا</a>
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <small class="text-white-50">
                            &copy; <?php echo date('Y'); ?> <?php echo Config::COMPANY_NAME; ?>. جميع الحقوق محفوظة.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تركيز تلقائي على حقل اسم المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            const usernameField = document.getElementById('username');
            if (usernameField && !usernameField.value) {
                usernameField.focus();
            }
        });
        
        // إظهار/إخفاء كلمة المرور
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('toggle-password')) {
                const passwordField = document.getElementById('password');
                const icon = e.target;
                
                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    passwordField.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            }
        });
    </script>
</body>
</html>
