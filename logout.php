<?php
/**
 * صفحة تسجيل الخروج - نظام التواصل الذكي
 * Logout Page - Smart Communication System
 */

require_once 'includes/functions.php';

// بدء الجلسة
startSecureSession();

// التحقق من تسجيل الدخول
if (isLoggedIn()) {
    try {
        $user_id = $_SESSION['user_id'];
        $session_token = $_SESSION['session_token'] ?? '';
        
        // تسجيل النشاط
        logUserActivity($user_id, 'logout', 'تسجيل خروج');
        
        // تحديث الجلسة في قاعدة البيانات
        if (!empty($session_token)) {
            $db = new Database();
            $query = "UPDATE user_sessions 
                     SET logout_time = NOW(), is_active = FALSE 
                     WHERE session_token = ? AND user_id = ?";
            
            $db->executeQuery($query, [$session_token, $user_id]);
        }
        
    } catch (Exception $e) {
        Config::logError("Logout error: " . $e->getMessage());
    }
}

// تدمير الجلسة
session_unset();
session_destroy();

// حذف ملفات تعريف الارتباط
if (isset($_COOKIE[session_name()])) {
    setcookie(session_name(), '', time() - 3600, '/');
}

// إعادة التوجيه إلى صفحة تسجيل الدخول
redirect('login.php');
?>
