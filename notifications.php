<?php
/**
 * صفحة الإشعارات - نظام التواصل الذكي
 * Notifications Page - Smart Communication System
 */

require_once 'includes/functions.php';

// بدء الجلسة والتحقق من تسجيل الدخول
startSecureSession();
if (!isLoggedIn()) {
    redirect('login.php');
}

$user_id = $_SESSION['user_id'];

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrf_token)) {
        showAlert('خطأ في التحقق من الأمان', 'danger');
    } else {
        $action = $_POST['action'] ?? '';
        
        try {
            $db = new Database();
            
            if ($action == 'mark_read') {
                $notification_id = (int)($_POST['notification_id'] ?? 0);
                if ($notification_id > 0) {
                    $update_query = "UPDATE notifications SET is_read = TRUE WHERE id = ? AND user_id = ?";
                    $db->executeQuery($update_query, [$notification_id, $user_id]);
                    showAlert('تم تحديد الإشعار كمقروء', 'success');
                }
            }
            
            elseif ($action == 'mark_all_read') {
                $update_query = "UPDATE notifications SET is_read = TRUE WHERE user_id = ?";
                $db->executeQuery($update_query, [$user_id]);
                showAlert('تم تحديد جميع الإشعارات كمقروءة', 'success');
            }
            
            elseif ($action == 'delete') {
                $notification_id = (int)($_POST['notification_id'] ?? 0);
                if ($notification_id > 0) {
                    $delete_query = "DELETE FROM notifications WHERE id = ? AND user_id = ?";
                    $db->executeQuery($delete_query, [$notification_id, $user_id]);
                    showAlert('تم حذف الإشعار', 'success');
                }
            }
            
            elseif ($action == 'delete_all_read') {
                $delete_query = "DELETE FROM notifications WHERE user_id = ? AND is_read = TRUE";
                $db->executeQuery($delete_query, [$user_id]);
                showAlert('تم حذف جميع الإشعارات المقروءة', 'success');
            }
            
        } catch (Exception $e) {
            Config::logError("Notifications action error: " . $e->getMessage());
            showAlert('حدث خطأ في تنفيذ العملية', 'danger');
        }
    }
    
    redirect('notifications.php');
}

// معاملات البحث والتصفية
$filter = Config::sanitizeInput($_GET['filter'] ?? 'all');
$type = Config::sanitizeInput($_GET['type'] ?? '');
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

try {
    $db = new Database();
    
    // بناء استعلام البحث
    $where_conditions = ["user_id = ?"];
    $params = [$user_id];
    
    // تصفية حسب الحالة
    if ($filter == 'unread') {
        $where_conditions[] = "is_read = FALSE";
    } elseif ($filter == 'read') {
        $where_conditions[] = "is_read = TRUE";
    }
    
    // تصفية حسب النوع
    if (!empty($type)) {
        $where_conditions[] = "type = ?";
        $params[] = $type;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // عدد النتائج الإجمالي
    $count_query = "SELECT COUNT(*) as total FROM notifications WHERE $where_clause";
    $count_stmt = $db->executeQuery($count_query, $params);
    $total_notifications = $count_stmt->fetch()['total'];
    $total_pages = ceil($total_notifications / $per_page);
    
    // استعلام الإشعارات
    $notifications_query = "SELECT * FROM notifications 
                           WHERE $where_clause 
                           ORDER BY created_at DESC 
                           LIMIT ? OFFSET ?";
    
    $params[] = $per_page;
    $params[] = $offset;
    
    $notifications_stmt = $db->executeQuery($notifications_query, $params);
    $notifications = $notifications_stmt->fetchAll();
    
    // إحصائيات الإشعارات
    $stats_query = "SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN is_read = FALSE THEN 1 END) as unread,
        COUNT(CASE WHEN is_read = TRUE THEN 1 END) as read,
        COUNT(CASE WHEN type = 'info' THEN 1 END) as info_count,
        COUNT(CASE WHEN type = 'success' THEN 1 END) as success_count,
        COUNT(CASE WHEN type = 'warning' THEN 1 END) as warning_count,
        COUNT(CASE WHEN type = 'error' THEN 1 END) as error_count
        FROM notifications 
        WHERE user_id = ?";
    
    $stats_stmt = $db->executeQuery($stats_query, [$user_id]);
    $stats = $stats_stmt->fetch();
    
} catch (Exception $e) {
    Config::logError("Notifications page error: " . $e->getMessage());
    $notifications = [];
    $total_notifications = 0;
    $total_pages = 1;
    $stats = ['total' => 0, 'unread' => 0, 'read' => 0, 'info_count' => 0, 'success_count' => 0, 'warning_count' => 0, 'error_count' => 0];
}

$page_title = "الإشعارات";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">الإشعارات</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="action" value="mark_all_read">
                            <button type="submit" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-check-double me-1"></i>
                                تحديد الكل كمقروء
                            </button>
                        </form>
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="action" value="delete_all_read">
                            <button type="submit" class="btn btn-sm btn-outline-danger" 
                                    onclick="return confirm('هل أنت متأكد من حذف جميع الإشعارات المقروءة؟')">
                                <i class="fas fa-trash me-1"></i>
                                حذف المقروءة
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- إحصائيات الإشعارات -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="h4 mb-0"><?php echo number_format($stats['total']); ?></div>
                                    <div>إجمالي الإشعارات</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-bell fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="h4 mb-0"><?php echo number_format($stats['unread']); ?></div>
                                    <div>غير مقروءة</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-bell-slash fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="h4 mb-0"><?php echo number_format($stats['read']); ?></div>
                                    <div>مقروءة</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="h4 mb-0"><?php echo number_format($stats['error_count']); ?></div>
                                    <div>تحذيرات</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج التصفية -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="filter" class="form-label">تصفية الإشعارات</label>
                            <select class="form-select" id="filter" name="filter">
                                <option value="all" <?php echo $filter == 'all' ? 'selected' : ''; ?>>جميع الإشعارات</option>
                                <option value="unread" <?php echo $filter == 'unread' ? 'selected' : ''; ?>>غير مقروءة</option>
                                <option value="read" <?php echo $filter == 'read' ? 'selected' : ''; ?>>مقروءة</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="type" class="form-label">نوع الإشعار</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">جميع الأنواع</option>
                                <option value="info" <?php echo $type == 'info' ? 'selected' : ''; ?>>معلومات</option>
                                <option value="success" <?php echo $type == 'success' ? 'selected' : ''; ?>>نجاح</option>
                                <option value="warning" <?php echo $type == 'warning' ? 'selected' : ''; ?>>تحذير</option>
                                <option value="error" <?php echo $type == 'error' ? 'selected' : ''; ?>>خطأ</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-1"></i>
                                    تصفية
                                </button>
                                <a href="notifications.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة الإشعارات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        الإشعارات (<?php echo number_format($total_notifications); ?> إشعار)
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($notifications)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-bell fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد إشعارات</p>
                    </div>
                    <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($notifications as $notification): ?>
                        <div class="list-group-item <?php echo !$notification['is_read'] ? 'list-group-item-warning' : ''; ?>">
                            <div class="d-flex w-100 justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-2">
                                        <?php
                                        $type_icons = [
                                            'info' => '<i class="fas fa-info-circle text-info me-2"></i>',
                                            'success' => '<i class="fas fa-check-circle text-success me-2"></i>',
                                            'warning' => '<i class="fas fa-exclamation-triangle text-warning me-2"></i>',
                                            'error' => '<i class="fas fa-times-circle text-danger me-2"></i>'
                                        ];
                                        echo $type_icons[$notification['type']] ?? '<i class="fas fa-bell text-secondary me-2"></i>';
                                        ?>
                                        <h6 class="mb-0"><?php echo htmlspecialchars($notification['title']); ?></h6>
                                        <?php if (!$notification['is_read']): ?>
                                        <span class="badge bg-primary ms-2">جديد</span>
                                        <?php endif; ?>
                                    </div>
                                    <p class="mb-2"><?php echo htmlspecialchars($notification['message']); ?></p>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        <?php echo formatDateArabic($notification['created_at']); ?>
                                    </small>
                                </div>
                                <div class="btn-group btn-group-sm ms-3">
                                    <?php if (!$notification['is_read']): ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                        <input type="hidden" name="action" value="mark_read">
                                        <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                        <button type="submit" class="btn btn-outline-primary" title="تحديد كمقروء">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </form>
                                    <?php endif; ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                        <button type="submit" class="btn btn-outline-danger" 
                                                onclick="return confirm('هل أنت متأكد من حذف هذا الإشعار؟')" 
                                                title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
                
                <!-- ترقيم الصفحات -->
                <?php if ($total_pages > 1): ?>
                <div class="card-footer">
                    <nav aria-label="ترقيم الصفحات">
                        <ul class="pagination justify-content-center mb-0">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">السابق</a>
                            </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                            </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">التالي</a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
</div>

<script>
// تحديث الصفحة كل 30 ثانية للحصول على إشعارات جديدة
setTimeout(function() {
    if (!document.hidden) {
        location.reload();
    }
}, 30000);

// تحديد جميع الإشعارات كمقروءة عند مغادرة الصفحة
window.addEventListener('beforeunload', function() {
    if (<?php echo $stats['unread']; ?> > 0) {
        navigator.sendBeacon('notifications.php', new FormData(document.querySelector('form[action="mark_all_read"]')));
    }
});
</script>

<?php include 'includes/footer.php'; ?>
