<?php
/**
 * صفحة الملف الشخصي - نظام التواصل الذكي
 * Profile Page - Smart Communication System
 */

require_once 'includes/functions.php';
require_once 'includes/Security.php';

// بدء الجلسة والتحقق من تسجيل الدخول
startSecureSession();
if (!isLoggedIn()) {
    redirect('login.php');
}

$user_id = $_SESSION['user_id'];

// معالجة تحديث الملف الشخصي
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrf_token)) {
        showAlert('خطأ في التحقق من الأمان', 'danger');
    } else {
        // جمع البيانات وتنظيفها
        $full_name = Config::sanitizeInput($_POST['full_name'] ?? '');
        $email = Config::sanitizeInput($_POST['email'] ?? '');
        $phone = Config::sanitizeInput($_POST['phone'] ?? '');
        $department = Config::sanitizeInput($_POST['department'] ?? '');
        
        // التحقق من صحة البيانات
        $errors = [];
        
        if (empty($full_name)) {
            $errors[] = 'الاسم الكامل مطلوب';
        }
        
        if (empty($email)) {
            $errors[] = 'البريد الإلكتروني مطلوب';
        } elseif (!Security::validateEmail($email)) {
            $errors[] = 'البريد الإلكتروني غير صحيح';
        }
        
        if (!empty($phone) && !validatePhone($phone)) {
            $errors[] = 'رقم الهاتف غير صحيح';
        }
        
        if (empty($errors)) {
            try {
                $db = new Database();
                
                // التحقق من عدم تكرار البريد الإلكتروني
                $check_email_query = "SELECT id FROM users WHERE email = ? AND id != ?";
                $check_email_stmt = $db->executeQuery($check_email_query, [$email, $user_id]);
                if ($check_email_stmt->fetch()) {
                    $errors[] = 'البريد الإلكتروني مستخدم من قبل';
                }
                
                if (empty($errors)) {
                    // تنسيق رقم الهاتف
                    if (!empty($phone)) {
                        $phone = formatPhone($phone);
                    }
                    
                    // تحديث بيانات المستخدم
                    $update_query = "UPDATE users SET full_name = ?, email = ?, phone = ?, department = ?, updated_at = NOW() WHERE id = ?";
                    $params = [$full_name, $email, $phone, $department, $user_id];
                    
                    $db->executeQuery($update_query, $params);
                    
                    // تحديث بيانات الجلسة
                    $_SESSION['user_name'] = $full_name;
                    
                    // تسجيل النشاط
                    logUserActivity($user_id, 'update_profile', "تحديث الملف الشخصي");
                    
                    showAlert('تم تحديث الملف الشخصي بنجاح', 'success');
                }
                
            } catch (Exception $e) {
                Config::logError("Update profile error: " . $e->getMessage());
                $errors[] = 'حدث خطأ في تحديث الملف الشخصي';
            }
        }
        
        if (!empty($errors)) {
            showAlert(implode('<br>', $errors), 'danger');
        }
    }
}

// الحصول على بيانات المستخدم الحالية
try {
    $db = new Database();
    $user_query = "SELECT * FROM users WHERE id = ?";
    $user_stmt = $db->executeQuery($user_query, [$user_id]);
    $user_data = $user_stmt->fetch();
    
    if (!$user_data) {
        showAlert('خطأ في تحميل بيانات المستخدم', 'danger');
        redirect('index.php');
    }
    
    // إحصائيات المستخدم
    $stats_query = "SELECT 
        (SELECT COUNT(*) FROM communication_log WHERE employee_id = ?) as total_communications,
        (SELECT COUNT(*) FROM communication_log WHERE employee_id = ? AND DATE(created_at) = CURDATE()) as today_communications,
        (SELECT COUNT(*) FROM clients WHERE assigned_employee_id = ?) as assigned_clients,
        (SELECT COUNT(*) FROM user_activity WHERE user_id = ? AND DATE(created_at) = CURDATE()) as today_activities";
    
    $stats_stmt = $db->executeQuery($stats_query, [$user_id, $user_id, $user_id, $user_id]);
    $user_stats = $stats_stmt->fetch();
    
} catch (Exception $e) {
    Config::logError("Profile page error: " . $e->getMessage());
    showAlert('حدث خطأ في تحميل البيانات', 'danger');
    redirect('index.php');
}

$page_title = "الملف الشخصي";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">الملف الشخصي</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="change-password.php" class="btn btn-outline-primary">
                        <i class="fas fa-key me-1"></i>
                        تغيير كلمة المرور
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- معلومات المستخدم -->
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="avatar bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px; font-size: 2rem;">
                                <i class="fas fa-user"></i>
                            </div>
                            <h5 class="card-title"><?php echo htmlspecialchars($user_data['full_name']); ?></h5>
                            <p class="text-muted">
                                <?php 
                                $roles = [
                                    'admin' => 'مدير النظام',
                                    'supervisor' => 'مشرف',
                                    'employee' => 'موظف'
                                ];
                                echo $roles[$user_data['role']] ?? $user_data['role'];
                                ?>
                            </p>
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="h4 text-primary"><?php echo number_format($user_stats['total_communications']); ?></div>
                                    <small class="text-muted">إجمالي التواصل</small>
                                </div>
                                <div class="col-6">
                                    <div class="h4 text-success"><?php echo number_format($user_stats['assigned_clients']); ?></div>
                                    <small class="text-muted">العملاء المعينين</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- إحصائيات اليوم -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                إحصائيات اليوم
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>التواصل اليوم</span>
                                <span class="badge bg-primary"><?php echo number_format($user_stats['today_communications']); ?></span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>الأنشطة اليوم</span>
                                <span class="badge bg-info"><?php echo number_format($user_stats['today_activities']); ?></span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <span>آخر دخول</span>
                                <small class="text-muted">
                                    <?php echo $user_data['last_login'] ? formatDateArabic($user_data['last_login']) : 'لم يسجل دخول'; ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- نموذج تحديث البيانات -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-edit me-2"></i>
                                تحديث البيانات الشخصية
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" class="needs-validation" novalidate>
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" 
                                               value="<?php echo htmlspecialchars($user_data['full_name']); ?>" 
                                               required>
                                        <div class="invalid-feedback">
                                            يرجى إدخال الاسم الكامل
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($user_data['email']); ?>" 
                                               required>
                                        <div class="invalid-feedback">
                                            يرجى إدخال بريد إلكتروني صحيح
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo htmlspecialchars($user_data['phone']); ?>"
                                               placeholder="07xxxxxxxx أو +962xxxxxxxx">
                                        <div class="form-text">مثال: 0791234567 أو +962791234567</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="department" class="form-label">القسم</label>
                                        <input type="text" class="form-control" id="department" name="department" 
                                               value="<?php echo htmlspecialchars($user_data['department']); ?>"
                                               placeholder="مثال: المبيعات، التسويق، الدعم الفني">
                                    </div>
                                </div>
                                
                                <!-- معلومات الحساب (للقراءة فقط) -->
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="username" class="form-label">اسم المستخدم</label>
                                        <input type="text" class="form-control" id="username" 
                                               value="<?php echo htmlspecialchars($user_data['username']); ?>" 
                                               readonly>
                                        <div class="form-text">لا يمكن تغيير اسم المستخدم</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="role" class="form-label">الصلاحية</label>
                                        <input type="text" class="form-control" id="role" 
                                               value="<?php echo $roles[$user_data['role']] ?? $user_data['role']; ?>" 
                                               readonly>
                                        <div class="form-text">يمكن للمدير تغيير الصلاحيات</div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="created_at" class="form-label">تاريخ إنشاء الحساب</label>
                                        <input type="text" class="form-control" id="created_at" 
                                               value="<?php echo formatDateArabic($user_data['created_at']); ?>" 
                                               readonly>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="is_active" class="form-label">حالة الحساب</label>
                                        <input type="text" class="form-control" id="is_active" 
                                               value="<?php echo $user_data['is_active'] ? 'نشط' : 'غير نشط'; ?>" 
                                               readonly>
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-between">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ التغييرات
                                    </button>
                                    <a href="change-password.php" class="btn btn-outline-warning">
                                        <i class="fas fa-key me-1"></i>
                                        تغيير كلمة المرور
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        const forms = document.getElementsByClassName('needs-validation');
        Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// تنسيق رقم الهاتف تلقائياً
document.getElementById('phone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.startsWith('962')) {
        value = '+' + value;
    } else if (value.startsWith('0')) {
        value = '+962' + value.substring(1);
    }
    e.target.value = value;
});
</script>

<?php include 'includes/footer.php'; ?>
