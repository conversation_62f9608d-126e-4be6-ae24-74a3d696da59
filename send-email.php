<?php
/**
 * صفحة إرسال البريد الإلكتروني - نظام التواصل الذكي
 * Send Email Page - Smart Communication System
 */

require_once 'includes/functions.php';
require_once 'includes/EmailService.php';

// بدء الجلسة والتحقق من تسجيل الدخول
startSecureSession();
if (!isLoggedIn()) {
    redirect('login.php');
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['user_role'];

// الحصول على معرف العميل من الرابط
$client_id = (int)($_GET['client_id'] ?? 0);
$selected_client = null;

// معالجة إرسال البريد الإلكتروني
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrf_token)) {
        showAlert('خطأ في التحقق من الأمان', 'danger');
    } else {
        $client_id = (int)($_POST['client_id'] ?? 0);
        $subject = Config::sanitizeInput($_POST['subject'] ?? '');
        $message = Config::sanitizeInput($_POST['message'] ?? '');
        $template_id = (int)($_POST['template_id'] ?? 0);
        
        // التحقق من صحة البيانات
        $errors = [];
        
        if ($client_id <= 0) {
            $errors[] = 'يجب اختيار عميل';
        }
        
        if (empty($subject)) {
            $errors[] = 'موضوع الرسالة مطلوب';
        }
        
        if (empty($message)) {
            $errors[] = 'محتوى الرسالة مطلوب';
        }
        
        if (empty($errors)) {
            try {
                $db = new Database();
                
                // الحصول على بيانات العميل
                $client_query = "SELECT * FROM clients WHERE id = ?";
                if ($user_role !== 'admin') {
                    $client_query .= " AND assigned_employee_id = ?";
                    $client_stmt = $db->executeQuery($client_query, [$client_id, $user_id]);
                } else {
                    $client_stmt = $db->executeQuery($client_query, [$client_id]);
                }
                
                $client = $client_stmt->fetch();
                
                if (!$client) {
                    $errors[] = 'العميل غير موجود أو ليس لديك صلاحية للوصول إليه';
                } elseif (empty($client['email'])) {
                    $errors[] = 'العميل لا يملك بريد إلكتروني';
                } else {
                    // إنشاء خدمة البريد الإلكتروني
                    $emailService = new EmailService();
                    
                    // معالجة المتغيرات في الرسالة
                    $variables = [
                        'client_name' => $client['name'],
                        'organization' => $client['organization'],
                        'company_name' => Config::COMPANY_NAME,
                        'employee_name' => $_SESSION['user_name'],
                        'current_date' => date('Y-m-d'),
                        'current_time' => date('H:i')
                    ];
                    
                    $processed_subject = $emailService->processTemplate($subject, $variables);
                    $processed_message = $emailService->processTemplate($message, $variables);
                    
                    // إرسال البريد الإلكتروني
                    $result = $emailService->sendEmail(
                        $client['email'],
                        $client['name'],
                        $processed_subject,
                        $processed_message,
                        $client_id,
                        $user_id,
                        $template_id > 0 ? $template_id : null
                    );
                    
                    if ($result['success']) {
                        logUserActivity($user_id, 'send_email', "إرسال بريد إلكتروني للعميل: {$client['name']}");
                        showAlert('تم إرسال البريد الإلكتروني بنجاح', 'success');
                        
                        // إعادة تعيين النموذج
                        $_POST = [];
                        $client_id = 0;
                    } else {
                        $errors[] = $result['message'];
                    }
                }
                
            } catch (Exception $e) {
                Config::logError("Send email error: " . $e->getMessage());
                $errors[] = 'حدث خطأ في إرسال البريد الإلكتروني';
            }
        }
        
        if (!empty($errors)) {
            showAlert(implode('<br>', $errors), 'danger');
        }
    }
}

// الحصول على بيانات العميل المحدد
if ($client_id > 0) {
    try {
        $db = new Database();
        $client_query = "SELECT * FROM clients WHERE id = ?";
        
        if ($user_role !== 'admin') {
            $client_query .= " AND assigned_employee_id = ?";
            $client_stmt = $db->executeQuery($client_query, [$client_id, $user_id]);
        } else {
            $client_stmt = $db->executeQuery($client_query, [$client_id]);
        }
        
        $selected_client = $client_stmt->fetch();
        
    } catch (Exception $e) {
        Config::logError("Get client error: " . $e->getMessage());
    }
}

// الحصول على قائمة العملاء
$clients = [];
try {
    $db = new Database();
    $clients_query = "SELECT id, name, organization, email FROM clients WHERE email IS NOT NULL AND email != ''";
    
    if ($user_role !== 'admin') {
        $clients_query .= " AND assigned_employee_id = ?";
        $clients_stmt = $db->executeQuery($clients_query, [$user_id]);
    } else {
        $clients_stmt = $db->executeQuery($clients_query);
    }
    
    $clients = $clients_stmt->fetchAll();
    
} catch (Exception $e) {
    Config::logError("Get clients error: " . $e->getMessage());
}

// الحصول على قوالب البريد الإلكتروني
$emailService = new EmailService();
$templates = $emailService->getEmailTemplates($user_id);

$page_title = "إرسال بريد إلكتروني";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إرسال بريد إلكتروني</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="communication-log.php" class="btn btn-outline-secondary">
                        <i class="fas fa-history me-1"></i>
                        سجل التواصل
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-envelope me-2"></i>
                                إنشاء رسالة جديدة
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" class="needs-validation" novalidate>
                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                
                                <div class="row">
                                    <div class="col-md-8 mb-3">
                                        <label for="client_id" class="form-label">العميل <span class="text-danger">*</span></label>
                                        <select class="form-select" id="client_id" name="client_id" required>
                                            <option value="">اختر العميل</option>
                                            <?php foreach ($clients as $client): ?>
                                            <option value="<?php echo $client['id']; ?>" 
                                                    data-email="<?php echo htmlspecialchars($client['email']); ?>"
                                                    <?php echo ($client_id == $client['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($client['name'] . ' - ' . $client['organization']); ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="invalid-feedback">
                                            يرجى اختيار العميل
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="template_id" class="form-label">قالب الرسالة</label>
                                        <select class="form-select" id="template_id" name="template_id">
                                            <option value="">اختر قالب (اختياري)</option>
                                            <?php foreach ($templates as $template): ?>
                                            <option value="<?php echo $template['id']; ?>" 
                                                    data-subject="<?php echo htmlspecialchars($template['subject']); ?>"
                                                    data-content="<?php echo htmlspecialchars($template['content']); ?>">
                                                <?php echo htmlspecialchars($template['name']); ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="subject" class="form-label">موضوع الرسالة <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="subject" name="subject" 
                                           value="<?php echo htmlspecialchars($_POST['subject'] ?? ''); ?>" 
                                           required>
                                    <div class="invalid-feedback">
                                        يرجى إدخال موضوع الرسالة
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="message" class="form-label">محتوى الرسالة <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="message" name="message" rows="10" required><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                                    <div class="invalid-feedback">
                                        يرجى إدخال محتوى الرسالة
                                    </div>
                                    <div class="form-text">
                                        يمكنك استخدام المتغيرات التالية: {{client_name}}, {{organization}}, {{company_name}}, {{employee_name}}, {{current_date}}, {{current_time}}
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-between">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-1"></i>
                                        إرسال البريد الإلكتروني
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="previewEmail()">
                                        <i class="fas fa-eye me-1"></i>
                                        معاينة
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <!-- معلومات العميل -->
                    <div class="card mb-3" id="clientInfo" style="display: none;">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-user me-2"></i>
                                معلومات العميل
                            </h6>
                        </div>
                        <div class="card-body" id="clientInfoContent">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                    
                    <!-- إرشادات -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                إرشادات
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb me-1"></i> نصائح مهمة:</h6>
                                <ul class="mb-0 small">
                                    <li>اختر العميل أولاً لعرض معلوماته</li>
                                    <li>يمكنك استخدام القوالب الجاهزة</li>
                                    <li>استخدم المتغيرات لتخصيص الرسالة</li>
                                    <li>تأكد من صحة البريد الإلكتروني</li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle me-1"></i> المتغيرات المتاحة:</h6>
                                <ul class="mb-0 small">
                                    <li><code>{{client_name}}</code> - اسم العميل</li>
                                    <li><code>{{organization}}</code> - اسم المؤسسة</li>
                                    <li><code>{{company_name}}</code> - اسم الشركة</li>
                                    <li><code>{{employee_name}}</code> - اسم الموظف</li>
                                    <li><code>{{current_date}}</code> - التاريخ الحالي</li>
                                    <li><code>{{current_time}}</code> - الوقت الحالي</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- نافذة المعاينة -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معاينة البريد الإلكتروني</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>إلى:</strong> <span id="previewTo"></span>
                </div>
                <div class="mb-3">
                    <strong>الموضوع:</strong> <span id="previewSubject"></span>
                </div>
                <div class="mb-3">
                    <strong>المحتوى:</strong>
                    <div id="previewContent" class="border p-3 bg-light"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
// بيانات العملاء
const clientsData = <?php echo json_encode($clients); ?>;

// تحديث معلومات العميل عند الاختيار
document.getElementById('client_id').addEventListener('change', function() {
    const clientId = this.value;
    const clientInfo = document.getElementById('clientInfo');
    const clientInfoContent = document.getElementById('clientInfoContent');
    
    if (clientId) {
        const client = clientsData.find(c => c.id == clientId);
        if (client) {
            clientInfoContent.innerHTML = `
                <p><strong>الاسم:</strong> ${client.name}</p>
                <p><strong>المؤسسة:</strong> ${client.organization}</p>
                <p><strong>البريد الإلكتروني:</strong> ${client.email}</p>
            `;
            clientInfo.style.display = 'block';
        }
    } else {
        clientInfo.style.display = 'none';
    }
});

// تطبيق القالب عند الاختيار
document.getElementById('template_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    if (selectedOption.value) {
        const subject = selectedOption.dataset.subject;
        const content = selectedOption.dataset.content;
        
        if (subject) {
            document.getElementById('subject').value = subject;
        }
        if (content) {
            document.getElementById('message').value = content;
        }
    }
});

// معاينة البريد الإلكتروني
function previewEmail() {
    const clientId = document.getElementById('client_id').value;
    const subject = document.getElementById('subject').value;
    const message = document.getElementById('message').value;
    
    if (!clientId || !subject || !message) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    const client = clientsData.find(c => c.id == clientId);
    if (!client) {
        alert('يرجى اختيار عميل صحيح');
        return;
    }
    
    // معالجة المتغيرات
    const variables = {
        'client_name': client.name,
        'organization': client.organization,
        'company_name': '<?php echo Config::COMPANY_NAME; ?>',
        'employee_name': '<?php echo $_SESSION['user_name']; ?>',
        'current_date': new Date().toLocaleDateString('ar-SA'),
        'current_time': new Date().toLocaleTimeString('ar-SA')
    };
    
    let processedSubject = subject;
    let processedMessage = message;
    
    for (const [key, value] of Object.entries(variables)) {
        const regex = new RegExp('{{' + key + '}}', 'g');
        processedSubject = processedSubject.replace(regex, value);
        processedMessage = processedMessage.replace(regex, value);
    }
    
    // عرض المعاينة
    document.getElementById('previewTo').textContent = `${client.name} <${client.email}>`;
    document.getElementById('previewSubject').textContent = processedSubject;
    document.getElementById('previewContent').innerHTML = processedMessage.replace(/\n/g, '<br>');
    
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
}

// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        const forms = document.getElementsByClassName('needs-validation');
        Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// تحديث معلومات العميل المحدد مسبقاً
<?php if ($selected_client): ?>
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('client_id').dispatchEvent(new Event('change'));
});
<?php endif; ?>
</script>

<?php include 'includes/footer.php'; ?>
