<?php
/**
 * صفحة إعدادات النظام - نظام التواصل الذكي
 * System Settings Page - Smart Communication System
 */

require_once 'includes/functions.php';

// بدء الجلسة والتحقق من تسجيل الدخول
startSecureSession();
if (!isLoggedIn()) {
    redirect('login.php');
}

// التحقق من الصلاحيات (مدير فقط)
if (!hasPermission('admin')) {
    showAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    redirect('index.php');
}

$user_id = $_SESSION['user_id'];

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (!verifyCSRFToken($csrf_token)) {
        showAlert('خطأ في التحقق من الأمان', 'danger');
    } else {
        try {
            $db = new Database();
            $updated_settings = [];
            
            // إعدادات الشركة
            $company_settings = [
                'company_name' => Config::sanitizeInput($_POST['company_name'] ?? ''),
                'company_email' => Config::sanitizeInput($_POST['company_email'] ?? ''),
                'company_phone' => Config::sanitizeInput($_POST['company_phone'] ?? ''),
                'company_address' => Config::sanitizeInput($_POST['company_address'] ?? ''),
                'company_website' => Config::sanitizeInput($_POST['company_website'] ?? '')
            ];
            
            // إعدادات البريد الإلكتروني
            $email_settings = [
                'smtp_host' => Config::sanitizeInput($_POST['smtp_host'] ?? ''),
                'smtp_port' => (int)($_POST['smtp_port'] ?? 587),
                'smtp_username' => Config::sanitizeInput($_POST['smtp_username'] ?? ''),
                'smtp_password' => Config::sanitizeInput($_POST['smtp_password'] ?? ''),
                'smtp_encryption' => Config::sanitizeInput($_POST['smtp_encryption'] ?? 'tls')
            ];
            
            // إعدادات واتساب
            $whatsapp_settings = [
                'whatsapp_api_url' => Config::sanitizeInput($_POST['whatsapp_api_url'] ?? ''),
                'whatsapp_api_token' => Config::sanitizeInput($_POST['whatsapp_api_token'] ?? ''),
                'whatsapp_enabled' => isset($_POST['whatsapp_enabled']) ? 1 : 0
            ];
            
            // إعدادات النظام
            $system_settings = [
                'session_timeout' => (int)($_POST['session_timeout'] ?? 3600),
                'max_login_attempts' => (int)($_POST['max_login_attempts'] ?? 5),
                'lockout_duration' => (int)($_POST['lockout_duration'] ?? 900),
                'enable_notifications' => isset($_POST['enable_notifications']) ? 1 : 0,
                'enable_email_logs' => isset($_POST['enable_email_logs']) ? 1 : 0,
                'timezone' => Config::sanitizeInput($_POST['timezone'] ?? 'Asia/Amman')
            ];
            
            // دمج جميع الإعدادات
            $all_settings = array_merge($company_settings, $email_settings, $whatsapp_settings, $system_settings);
            
            // حفظ الإعدادات في قاعدة البيانات
            foreach ($all_settings as $key => $value) {
                $check_query = "SELECT id FROM system_settings WHERE setting_key = ?";
                $check_stmt = $db->executeQuery($check_query, [$key]);
                
                if ($check_stmt->fetch()) {
                    // تحديث الإعداد الموجود
                    $update_query = "UPDATE system_settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?";
                    $db->executeQuery($update_query, [$value, $key]);
                } else {
                    // إضافة إعداد جديد
                    $insert_query = "INSERT INTO system_settings (setting_key, setting_value, created_at) VALUES (?, ?, NOW())";
                    $db->executeQuery($insert_query, [$key, $value]);
                }
                
                $updated_settings[] = $key;
            }
            
            // تسجيل النشاط
            logUserActivity($user_id, 'update_system_settings', "تحديث إعدادات النظام: " . implode(', ', $updated_settings));
            
            showAlert('تم حفظ إعدادات النظام بنجاح', 'success');
            
        } catch (Exception $e) {
            Config::logError("System settings error: " . $e->getMessage());
            showAlert('حدث خطأ في حفظ الإعدادات', 'danger');
        }
    }
}

// الحصول على الإعدادات الحالية
try {
    $db = new Database();
    $settings_query = "SELECT setting_key, setting_value FROM system_settings";
    $settings_stmt = $db->executeQuery($settings_query);
    $settings_data = $settings_stmt->fetchAll();
    
    $settings = [];
    foreach ($settings_data as $setting) {
        $settings[$setting['setting_key']] = $setting['setting_value'];
    }
    
} catch (Exception $e) {
    Config::logError("Get settings error: " . $e->getMessage());
    $settings = [];
}

$page_title = "إعدادات النظام";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إعدادات النظام</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="test-system.php" class="btn btn-outline-info">
                        <i class="fas fa-vial me-1"></i>
                        اختبار النظام
                    </a>
                </div>
            </div>

            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <!-- إعدادات الشركة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-building me-2"></i>
                            إعدادات الشركة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="company_name" class="form-label">اسم الشركة</label>
                                <input type="text" class="form-control" id="company_name" name="company_name" 
                                       value="<?php echo htmlspecialchars($settings['company_name'] ?? Config::COMPANY_NAME); ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="company_email" class="form-label">البريد الإلكتروني للشركة</label>
                                <input type="email" class="form-control" id="company_email" name="company_email" 
                                       value="<?php echo htmlspecialchars($settings['company_email'] ?? ''); ?>">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="company_phone" class="form-label">هاتف الشركة</label>
                                <input type="tel" class="form-control" id="company_phone" name="company_phone" 
                                       value="<?php echo htmlspecialchars($settings['company_phone'] ?? ''); ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="company_website" class="form-label">موقع الشركة</label>
                                <input type="url" class="form-control" id="company_website" name="company_website" 
                                       value="<?php echo htmlspecialchars($settings['company_website'] ?? ''); ?>">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="company_address" class="form-label">عنوان الشركة</label>
                            <textarea class="form-control" id="company_address" name="company_address" rows="2"><?php echo htmlspecialchars($settings['company_address'] ?? ''); ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- إعدادات البريد الإلكتروني -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-envelope me-2"></i>
                            إعدادات البريد الإلكتروني (SMTP)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="smtp_host" class="form-label">خادم SMTP</label>
                                <input type="text" class="form-control" id="smtp_host" name="smtp_host" 
                                       value="<?php echo htmlspecialchars($settings['smtp_host'] ?? 'smtp.gmail.com'); ?>"
                                       placeholder="smtp.gmail.com">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="smtp_port" class="form-label">منفذ SMTP</label>
                                <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                                       value="<?php echo htmlspecialchars($settings['smtp_port'] ?? '587'); ?>"
                                       placeholder="587">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="smtp_username" class="form-label">اسم المستخدم (البريد الإلكتروني)</label>
                                <input type="email" class="form-control" id="smtp_username" name="smtp_username" 
                                       value="<?php echo htmlspecialchars($settings['smtp_username'] ?? ''); ?>"
                                       placeholder="<EMAIL>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="smtp_password" class="form-label">كلمة المرور (App Password)</label>
                                <input type="password" class="form-control" id="smtp_password" name="smtp_password" 
                                       value="<?php echo htmlspecialchars($settings['smtp_password'] ?? ''); ?>"
                                       placeholder="App Password">
                                <div class="form-text">استخدم App Password لـ Gmail</div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="smtp_encryption" class="form-label">نوع التشفير</label>
                                <select class="form-select" id="smtp_encryption" name="smtp_encryption">
                                    <option value="tls" <?php echo ($settings['smtp_encryption'] ?? 'tls') == 'tls' ? 'selected' : ''; ?>>TLS</option>
                                    <option value="ssl" <?php echo ($settings['smtp_encryption'] ?? '') == 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إعدادات واتساب -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fab fa-whatsapp me-2"></i>
                            إعدادات واتساب
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="whatsapp_enabled" name="whatsapp_enabled" 
                                       <?php echo ($settings['whatsapp_enabled'] ?? 0) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="whatsapp_enabled">
                                    تفعيل واتساب API
                                </label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="whatsapp_api_url" class="form-label">رابط واتساب API</label>
                                <input type="url" class="form-control" id="whatsapp_api_url" name="whatsapp_api_url" 
                                       value="<?php echo htmlspecialchars($settings['whatsapp_api_url'] ?? ''); ?>"
                                       placeholder="https://api.whatsapp.com/send">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="whatsapp_api_token" class="form-label">رمز واتساب API</label>
                                <input type="password" class="form-control" id="whatsapp_api_token" name="whatsapp_api_token" 
                                       value="<?php echo htmlspecialchars($settings['whatsapp_api_token'] ?? ''); ?>"
                                       placeholder="API Token">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إعدادات الأمان -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            إعدادات الأمان
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="session_timeout" class="form-label">انتهاء صلاحية الجلسة (ثانية)</label>
                                <input type="number" class="form-control" id="session_timeout" name="session_timeout" 
                                       value="<?php echo htmlspecialchars($settings['session_timeout'] ?? '3600'); ?>"
                                       min="300" max="86400">
                                <div class="form-text">3600 = ساعة واحدة</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="max_login_attempts" class="form-label">محاولات تسجيل الدخول القصوى</label>
                                <input type="number" class="form-control" id="max_login_attempts" name="max_login_attempts" 
                                       value="<?php echo htmlspecialchars($settings['max_login_attempts'] ?? '5'); ?>"
                                       min="3" max="10">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="lockout_duration" class="form-label">مدة الحظر (ثانية)</label>
                                <input type="number" class="form-control" id="lockout_duration" name="lockout_duration" 
                                       value="<?php echo htmlspecialchars($settings['lockout_duration'] ?? '900'); ?>"
                                       min="300" max="3600">
                                <div class="form-text">900 = 15 دقيقة</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إعدادات عامة -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            إعدادات عامة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="timezone" class="form-label">المنطقة الزمنية</label>
                                <select class="form-select" id="timezone" name="timezone">
                                    <option value="Asia/Amman" <?php echo ($settings['timezone'] ?? 'Asia/Amman') == 'Asia/Amman' ? 'selected' : ''; ?>>عمان (GMT+3)</option>
                                    <option value="Asia/Riyadh" <?php echo ($settings['timezone'] ?? '') == 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض (GMT+3)</option>
                                    <option value="Asia/Dubai" <?php echo ($settings['timezone'] ?? '') == 'Asia/Dubai' ? 'selected' : ''; ?>>دبي (GMT+4)</option>
                                    <option value="Africa/Cairo" <?php echo ($settings['timezone'] ?? '') == 'Africa/Cairo' ? 'selected' : ''; ?>>القاهرة (GMT+2)</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enable_notifications" name="enable_notifications" 
                                           <?php echo ($settings['enable_notifications'] ?? 1) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="enable_notifications">
                                        تفعيل الإشعارات
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enable_email_logs" name="enable_email_logs" 
                                           <?php echo ($settings['enable_email_logs'] ?? 1) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="enable_email_logs">
                                        تفعيل سجلات البريد الإلكتروني
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                حفظ جميع الإعدادات
                            </button>
                            <div>
                                <a href="test-system.php" class="btn btn-outline-info me-2">
                                    <i class="fas fa-vial me-1"></i>
                                    اختبار النظام
                                </a>
                                <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                                    <i class="fas fa-undo me-1"></i>
                                    إعادة تعيين
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </main>
    </div>
</div>

<script>
// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        const forms = document.getElementsByClassName('needs-validation');
        Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// إعادة تعيين النموذج
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
        location.reload();
    }
}

// تفعيل/تعطيل حقول واتساب
document.getElementById('whatsapp_enabled').addEventListener('change', function() {
    const whatsappFields = ['whatsapp_api_url', 'whatsapp_api_token'];
    whatsappFields.forEach(fieldId => {
        document.getElementById(fieldId).disabled = !this.checked;
    });
});

// تشغيل التحقق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('whatsapp_enabled').dispatchEvent(new Event('change'));
});
</script>

<?php include 'includes/footer.php'; ?>
