<?php
/**
 * اختبار التقارير - نظام التواصل الذكي
 * Test Reports - Smart Communication System
 */

require_once 'includes/functions.php';

// بدء الجلسة والتحقق من تسجيل الدخول
startSecureSession();
if (!isLoggedIn()) {
    redirect('login.php');
}

// التحقق من الصلاحيات
if (!hasPermission('supervisor')) {
    showAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    redirect('index.php');
}

$page_title = "اختبار التقارير";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">اختبار التقارير</h1>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">اختبار التقارير</h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group">
                                <a href="client-reports.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-users text-primary me-2"></i>
                                    تقارير العملاء
                                    <span class="badge bg-success ms-auto">✓</span>
                                </a>
                                <a href="client-assignments.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-user-plus text-info me-2"></i>
                                    تعيين العملاء
                                    <span class="badge bg-success ms-auto">✓</span>
                                </a>
                                <a href="communication-reports.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-comments text-success me-2"></i>
                                    تقارير التواصل
                                    <span class="badge bg-success ms-auto">✓</span>
                                </a>
                                <a href="employee-reports.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-user-tie text-warning me-2"></i>
                                    تقارير الموظفين
                                    <span class="badge bg-success ms-auto">✓</span>
                                </a>
                                <a href="notifications.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-bell text-danger me-2"></i>
                                    الإشعارات
                                    <span class="badge bg-success ms-auto">✓</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">حالة النظام</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            try {
                                $db = new Database();
                                
                                // اختبار قاعدة البيانات
                                $test_query = "SELECT COUNT(*) as count FROM users";
                                $test_stmt = $db->executeQuery($test_query);
                                $users_count = $test_stmt->fetch()['count'];
                                
                                $test_query = "SELECT COUNT(*) as count FROM clients";
                                $test_stmt = $db->executeQuery($test_query);
                                $clients_count = $test_stmt->fetch()['count'];
                                
                                $test_query = "SELECT COUNT(*) as count FROM communication_log";
                                $test_stmt = $db->executeQuery($test_query);
                                $communications_count = $test_stmt->fetch()['count'];
                                
                                echo '<div class="alert alert-success">';
                                echo '<h6><i class="fas fa-check-circle me-2"></i>قاعدة البيانات تعمل بشكل صحيح</h6>';
                                echo '<ul class="mb-0">';
                                echo "<li>المستخدمين: " . number_format($users_count) . "</li>";
                                echo "<li>العملاء: " . number_format($clients_count) . "</li>";
                                echo "<li>سجل التواصل: " . number_format($communications_count) . "</li>";
                                echo '</ul>';
                                echo '</div>';
                                
                            } catch (Exception $e) {
                                echo '<div class="alert alert-danger">';
                                echo '<h6><i class="fas fa-times-circle me-2"></i>خطأ في قاعدة البيانات</h6>';
                                echo '<p class="mb-0">' . htmlspecialchars($e->getMessage()) . '</p>';
                                echo '</div>';
                            }
                            ?>
                            
                            <div class="mt-3">
                                <h6>الملفات المطلوبة:</h6>
                                <ul class="list-unstyled">
                                    <?php
                                    $required_files = [
                                        'client-reports.php' => 'تقارير العملاء',
                                        'client-assignments.php' => 'تعيين العملاء',
                                        'communication-reports.php' => 'تقارير التواصل',
                                        'employee-reports.php' => 'تقارير الموظفين',
                                        'notifications.php' => 'الإشعارات'
                                    ];
                                    
                                    foreach ($required_files as $file => $name) {
                                        if (file_exists($file)) {
                                            echo '<li><i class="fas fa-check text-success me-2"></i>' . $name . '</li>';
                                        } else {
                                            echo '<li><i class="fas fa-times text-danger me-2"></i>' . $name . ' (مفقود)</li>';
                                        }
                                    }
                                    ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">إحصائيات سريعة</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            try {
                                $db = new Database();
                                
                                // إحصائيات العملاء
                                $stats_query = "SELECT 
                                    COUNT(*) as total_clients,
                                    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_clients,
                                    COUNT(CASE WHEN assigned_employee_id IS NOT NULL THEN 1 END) as assigned_clients
                                    FROM clients";
                                $stats_stmt = $db->executeQuery($stats_query);
                                $client_stats = $stats_stmt->fetch();
                                
                                // إحصائيات التواصل
                                $comm_query = "SELECT 
                                    COUNT(*) as total_communications,
                                    COUNT(CASE WHEN communication_type = 'email' THEN 1 END) as email_count,
                                    COUNT(CASE WHEN communication_type = 'whatsapp' THEN 1 END) as whatsapp_count,
                                    COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_count
                                    FROM communication_log";
                                $comm_stmt = $db->executeQuery($comm_query);
                                $comm_stats = $comm_stmt->fetch();
                                
                                // إحصائيات الموظفين
                                $emp_query = "SELECT 
                                    COUNT(*) as total_employees,
                                    COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_employees,
                                    COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_count
                                    FROM users 
                                    WHERE role IN ('employee', 'supervisor', 'admin')";
                                $emp_stmt = $db->executeQuery($emp_query);
                                $emp_stats = $emp_stmt->fetch();
                                
                                ?>
                                <div class="row">
                                    <div class="col-md-4">
                                        <h6>إحصائيات العملاء</h6>
                                        <ul class="list-unstyled">
                                            <li>إجمالي العملاء: <strong><?php echo number_format($client_stats['total_clients']); ?></strong></li>
                                            <li>عملاء نشطين: <strong><?php echo number_format($client_stats['active_clients']); ?></strong></li>
                                            <li>عملاء معينين: <strong><?php echo number_format($client_stats['assigned_clients']); ?></strong></li>
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>إحصائيات التواصل</h6>
                                        <ul class="list-unstyled">
                                            <li>إجمالي التواصل: <strong><?php echo number_format($comm_stats['total_communications']); ?></strong></li>
                                            <li>رسائل بريد: <strong><?php echo number_format($comm_stats['email_count']); ?></strong></li>
                                            <li>رسائل واتساب: <strong><?php echo number_format($comm_stats['whatsapp_count']); ?></strong></li>
                                            <li>رسائل مرسلة: <strong><?php echo number_format($comm_stats['sent_count']); ?></strong></li>
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>إحصائيات الموظفين</h6>
                                        <ul class="list-unstyled">
                                            <li>إجمالي الموظفين: <strong><?php echo number_format($emp_stats['total_employees']); ?></strong></li>
                                            <li>موظفين نشطين: <strong><?php echo number_format($emp_stats['active_employees']); ?></strong></li>
                                            <li>مديرين: <strong><?php echo number_format($emp_stats['admin_count']); ?></strong></li>
                                        </ul>
                                    </div>
                                </div>
                                <?php
                                
                            } catch (Exception $e) {
                                echo '<div class="alert alert-danger">';
                                echo '<p class="mb-0">خطأ في تحميل الإحصائيات: ' . htmlspecialchars($e->getMessage()) . '</p>';
                                echo '</div>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
