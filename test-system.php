<?php
/**
 * اختبار النظام - نظام التواصل الذكي
 * System Test - Smart Communication System
 */

require_once 'includes/functions.php';
require_once 'includes/WhatsAppService.php';
require_once 'includes/Security.php';

// تحديد خدمة البريد الإلكتروني المناسبة
if (file_exists('includes/EmailService.php') && file_exists('vendor/autoload.php')) {
    require_once 'includes/EmailService.php';
    $emailServiceClass = 'EmailService';
} else {
    require_once 'includes/SimpleEmailService.php';
    $emailServiceClass = 'SimpleEmailService';
}

// التحقق من الصلاحيات
startSecureSession();
if (!isLoggedIn() || !hasPermission('admin')) {
    die('غير مسموح لك بالوصول لهذه الصفحة');
}

$tests = [];
$overall_status = true;

/**
 * اختبار الاتصال بقاعدة البيانات
 */
function testDatabaseConnection() {
    try {
        $db = new Database();
        $stmt = $db->executeQuery("SELECT 1");
        return [
            'status' => true,
            'message' => 'الاتصال بقاعدة البيانات يعمل بشكل صحيح'
        ];
    } catch (Exception $e) {
        return [
            'status' => false,
            'message' => 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage()
        ];
    }
}

/**
 * اختبار الجداول المطلوبة
 */
function testDatabaseTables() {
    $required_tables = [
        'users', 'clients', 'communication_log', 'notifications', 
        'system_settings', 'user_sessions', 'message_templates'
    ];
    
    try {
        $db = new Database();
        $missing_tables = [];
        
        foreach ($required_tables as $table) {
            $stmt = $db->executeQuery("SHOW TABLES LIKE ?", [$table]);
            if (!$stmt->fetch()) {
                $missing_tables[] = $table;
            }
        }
        
        if (empty($missing_tables)) {
            return [
                'status' => true,
                'message' => 'جميع الجداول المطلوبة موجودة'
            ];
        } else {
            return [
                'status' => false,
                'message' => 'الجداول المفقودة: ' . implode(', ', $missing_tables)
            ];
        }
    } catch (Exception $e) {
        return [
            'status' => false,
            'message' => 'خطأ في فحص الجداول: ' . $e->getMessage()
        ];
    }
}

/**
 * اختبار إعدادات البريد الإلكتروني
 */
function testEmailSettings() {
    global $emailServiceClass;
    try {
        require_once 'includes/SimpleEmailService.php';
        $emailService = new $emailServiceClass();
        $result = $emailService->testEmailSettings();

        return [
            'status' => $result['success'],
            'message' => $result['message']
        ];
    } catch (Exception $e) {
        return [
            'status' => false,
            'message' => 'خطأ في اختبار البريد الإلكتروني: ' . $e->getMessage()
        ];
    }
}

/**
 * اختبار إعدادات واتساب
 */
function testWhatsAppSettings() {
    try {
        $whatsappService = new WhatsAppService();
        $result = $whatsappService->testWhatsAppSettings();
        
        return [
            'status' => $result['success'],
            'message' => $result['message']
        ];
    } catch (Exception $e) {
        return [
            'status' => false,
            'message' => 'خطأ في اختبار واتساب: ' . $e->getMessage()
        ];
    }
}

/**
 * اختبار الأمان والتشفير
 */
function testSecurity() {
    try {
        // اختبار التشفير
        $test_data = 'test data for encryption';
        $encrypted = Security::encryptData($test_data);
        $decrypted = Security::decryptData($encrypted);
        
        if ($decrypted !== $test_data) {
            return [
                'status' => false,
                'message' => 'فشل في اختبار التشفير'
            ];
        }
        
        // اختبار CSRF
        $token = Security::generateCSRFToken();
        if (!Security::verifyCSRFToken($token)) {
            return [
                'status' => false,
                'message' => 'فشل في اختبار CSRF'
            ];
        }
        
        // اختبار تنظيف البيانات
        $dirty_input = '<script>alert("xss")</script>';
        $clean_input = Security::sanitizeInput($dirty_input);
        if (strpos($clean_input, '<script>') !== false) {
            return [
                'status' => false,
                'message' => 'فشل في تنظيف البيانات من XSS'
            ];
        }
        
        return [
            'status' => true,
            'message' => 'جميع اختبارات الأمان نجحت'
        ];
    } catch (Exception $e) {
        return [
            'status' => false,
            'message' => 'خطأ في اختبار الأمان: ' . $e->getMessage()
        ];
    }
}

/**
 * اختبار الملفات المطلوبة
 */
function testRequiredFiles() {
    $required_files = [
        'config/database.php',
        'includes/functions.php',
        'includes/EmailService.php',
        'includes/WhatsAppService.php',
        'includes/Security.php',
        'assets/css/style.css',
        'assets/js/app.js'
    ];
    
    $missing_files = [];
    
    foreach ($required_files as $file) {
        if (!file_exists($file)) {
            $missing_files[] = $file;
        }
    }
    
    if (empty($missing_files)) {
        return [
            'status' => true,
            'message' => 'جميع الملفات المطلوبة موجودة'
        ];
    } else {
        return [
            'status' => false,
            'message' => 'الملفات المفقودة: ' . implode(', ', $missing_files)
        ];
    }
}

/**
 * اختبار الصلاحيات
 */
function testPermissions() {
    $directories = ['logs/', 'uploads/', 'backups/'];
    $issues = [];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                $issues[] = "لا يمكن إنشاء مجلد: $dir";
            }
        }
        
        if (!is_writable($dir)) {
            $issues[] = "لا يمكن الكتابة في مجلد: $dir";
        }
    }
    
    if (empty($issues)) {
        return [
            'status' => true,
            'message' => 'جميع صلاحيات المجلدات صحيحة'
        ];
    } else {
        return [
            'status' => false,
            'message' => implode(', ', $issues)
        ];
    }
}

/**
 * اختبار PHP Extensions
 */
function testPHPExtensions() {
    $required_extensions = [
        'pdo', 'pdo_mysql', 'curl', 'openssl', 'mbstring', 'json'
    ];
    
    $missing_extensions = [];
    
    foreach ($required_extensions as $ext) {
        if (!extension_loaded($ext)) {
            $missing_extensions[] = $ext;
        }
    }
    
    if (empty($missing_extensions)) {
        return [
            'status' => true,
            'message' => 'جميع PHP Extensions المطلوبة متوفرة'
        ];
    } else {
        return [
            'status' => false,
            'message' => 'Extensions المفقودة: ' . implode(', ', $missing_extensions)
        ];
    }
}

// تشغيل الاختبارات
$tests['database_connection'] = testDatabaseConnection();
$tests['database_tables'] = testDatabaseTables();
$tests['email_settings'] = testEmailSettings();
$tests['whatsapp_settings'] = testWhatsAppSettings();
$tests['security'] = testSecurity();
$tests['required_files'] = testRequiredFiles();
$tests['permissions'] = testPermissions();
$tests['php_extensions'] = testPHPExtensions();

// تحديد الحالة العامة
foreach ($tests as $test) {
    if (!$test['status']) {
        $overall_status = false;
        break;
    }
}

$page_title = "اختبار النظام";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">اختبار النظام</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-outline-secondary" onclick="window.location.reload()">
                        <i class="fas fa-sync me-1"></i>
                        إعادة الاختبار
                    </button>
                </div>
            </div>

            <!-- الحالة العامة -->
            <div class="alert <?php echo $overall_status ? 'alert-success' : 'alert-danger'; ?> mb-4">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas <?php echo $overall_status ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> fa-2x"></i>
                    </div>
                    <div>
                        <h5 class="mb-1">
                            <?php echo $overall_status ? 'النظام يعمل بشكل صحيح' : 'يوجد مشاكل في النظام'; ?>
                        </h5>
                        <p class="mb-0">
                            <?php echo $overall_status ? 'جميع الاختبارات نجحت بنجاح' : 'يرجى حل المشاكل المذكورة أدناه'; ?>
                        </p>
                    </div>
                </div>
            </div>

            <!-- نتائج الاختبارات -->
            <div class="row">
                <?php 
                $test_titles = [
                    'database_connection' => 'الاتصال بقاعدة البيانات',
                    'database_tables' => 'جداول قاعدة البيانات',
                    'email_settings' => 'إعدادات البريد الإلكتروني',
                    'whatsapp_settings' => 'إعدادات واتساب',
                    'security' => 'الأمان والتشفير',
                    'required_files' => 'الملفات المطلوبة',
                    'permissions' => 'صلاحيات المجلدات',
                    'php_extensions' => 'PHP Extensions'
                ];
                
                foreach ($tests as $test_key => $test_result):
                ?>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="fas <?php echo $test_result['status'] ? 'fa-check-circle text-success' : 'fa-times-circle text-danger'; ?> fa-2x"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="card-title mb-1"><?php echo $test_titles[$test_key]; ?></h6>
                                    <p class="card-text mb-0 small">
                                        <?php echo htmlspecialchars($test_result['message']); ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- معلومات النظام -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>إصدار PHP:</strong></td>
                                    <td><?php echo PHP_VERSION; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>خادم الويب:</strong></td>
                                    <td><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف'; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>نظام التشغيل:</strong></td>
                                    <td><?php echo PHP_OS; ?></td>
                                </tr>
                                <tr>
                                    <td><strong>الذاكرة المتاحة:</strong></td>
                                    <td><?php echo ini_get('memory_limit'); ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>المنطقة الزمنية:</strong></td>
                                    <td><?php echo date_default_timezone_get(); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>التاريخ والوقت:</strong></td>
                                    <td><?php echo date('Y-m-d H:i:s'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>مساحة القرص:</strong></td>
                                    <td><?php echo round(disk_free_space('.') / 1024 / 1024 / 1024, 2); ?> GB</td>
                                </tr>
                                <tr>
                                    <td><strong>إصدار النظام:</strong></td>
                                    <td>1.0.0</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (!$overall_status): ?>
            <!-- إرشادات حل المشاكل -->
            <div class="card mt-4">
                <div class="card-header bg-warning">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools me-2"></i>
                        إرشادات حل المشاكل
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-1"></i> خطوات حل المشاكل:</h6>
                        <ol>
                            <li>تأكد من تشغيل Apache و MySQL في XAMPP</li>
                            <li>تحقق من إعدادات قاعدة البيانات في config/database.php</li>
                            <li>تأكد من وجود جميع الملفات المطلوبة</li>
                            <li>تحقق من صلاحيات المجلدات</li>
                            <li>قم بتثبيت PHP Extensions المفقودة</li>
                            <li>راجع ملف logs/error.log للمزيد من التفاصيل</li>
                        </ol>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
