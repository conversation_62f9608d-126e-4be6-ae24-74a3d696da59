<?php
/**
 * صفحة قائمة المستخدمين - نظام التواصل الذكي
 * Users List Page - Smart Communication System
 */

require_once 'includes/functions.php';

// بدء الجلسة والتحقق من تسجيل الدخول
startSecureSession();
if (!isLoggedIn()) {
    redirect('login.php');
}

// التحقق من الصلاحيات (مدير فقط)
if (!hasPermission('admin')) {
    showAlert('ليس لديك صلاحية للوصول لهذه الصفحة', 'danger');
    redirect('index.php');
}

$user_id = $_SESSION['user_id'];

// معالجة الحذف
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'delete') {
    $delete_user_id = (int)($_POST['user_id'] ?? 0);
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrf_token) && $delete_user_id > 0) {
        // منع حذف المستخدم الحالي
        if ($delete_user_id == $user_id) {
            showAlert('لا يمكنك حذف حسابك الخاص', 'danger');
        } else {
            try {
                $db = new Database();
                
                // التحقق من وجود المستخدم
                $check_query = "SELECT username, full_name FROM users WHERE id = ?";
                $check_stmt = $db->executeQuery($check_query, [$delete_user_id]);
                $user_to_delete = $check_stmt->fetch();
                
                if ($user_to_delete) {
                    // حذف المستخدم
                    $delete_query = "DELETE FROM users WHERE id = ?";
                    $db->executeQuery($delete_query, [$delete_user_id]);
                    
                    logUserActivity($user_id, 'delete_user', "حذف المستخدم: {$user_to_delete['username']} ({$user_to_delete['full_name']})");
                    showAlert('تم حذف المستخدم بنجاح', 'success');
                } else {
                    showAlert('المستخدم غير موجود', 'danger');
                }
                
            } catch (Exception $e) {
                Config::logError("Delete user error: " . $e->getMessage());
                showAlert('حدث خطأ في حذف المستخدم', 'danger');
            }
        }
    }
    
    redirect('users.php');
}

// معالجة تغيير الحالة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'toggle_status') {
    $toggle_user_id = (int)($_POST['user_id'] ?? 0);
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrf_token) && $toggle_user_id > 0) {
        // منع تعطيل المستخدم الحالي
        if ($toggle_user_id == $user_id) {
            showAlert('لا يمكنك تعطيل حسابك الخاص', 'danger');
        } else {
            try {
                $db = new Database();
                
                // تبديل الحالة
                $toggle_query = "UPDATE users SET is_active = NOT is_active WHERE id = ?";
                $db->executeQuery($toggle_query, [$toggle_user_id]);
                
                logUserActivity($user_id, 'toggle_user_status', "تغيير حالة المستخدم رقم: $toggle_user_id");
                showAlert('تم تغيير حالة المستخدم بنجاح', 'success');
                
            } catch (Exception $e) {
                Config::logError("Toggle user status error: " . $e->getMessage());
                showAlert('حدث خطأ في تغيير حالة المستخدم', 'danger');
            }
        }
    }
    
    redirect('users.php');
}

// معاملات البحث والتصفية
$search = Config::sanitizeInput($_GET['search'] ?? '');
$role = Config::sanitizeInput($_GET['role'] ?? '');
$status = Config::sanitizeInput($_GET['status'] ?? '');
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

try {
    $db = new Database();
    
    // بناء استعلام البحث
    $where_conditions = [];
    $params = [];
    
    // البحث النصي
    if (!empty($search)) {
        $where_conditions[] = "(username LIKE ? OR full_name LIKE ? OR email LIKE ? OR phone LIKE ?)";
        $search_param = "%$search%";
        $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
    }
    
    // تصفية الصلاحية
    if (!empty($role)) {
        $where_conditions[] = "role = ?";
        $params[] = $role;
    }
    
    // تصفية الحالة
    if (!empty($status)) {
        if ($status == 'active') {
            $where_conditions[] = "is_active = TRUE";
        } elseif ($status == 'inactive') {
            $where_conditions[] = "is_active = FALSE";
        }
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // عدد النتائج الإجمالي
    $count_query = "SELECT COUNT(*) as total FROM users $where_clause";
    $count_stmt = $db->executeQuery($count_query, $params);
    $total_users = $count_stmt->fetch()['total'];
    $total_pages = ceil($total_users / $per_page);
    
    // استعلام المستخدمين
    $users_query = "SELECT id, username, email, full_name, role, phone, department, is_active, 
                           last_login, created_at 
                    FROM users 
                    $where_clause 
                    ORDER BY created_at DESC 
                    LIMIT ? OFFSET ?";
    
    $params[] = $per_page;
    $params[] = $offset;
    
    $users_stmt = $db->executeQuery($users_query, $params);
    $users = $users_stmt->fetchAll();
    
    // إحصائيات سريعة
    $stats_query = "SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active,
        COUNT(CASE WHEN role = 'admin' THEN 1 END) as admins,
        COUNT(CASE WHEN role = 'supervisor' THEN 1 END) as supervisors,
        COUNT(CASE WHEN role = 'employee' THEN 1 END) as employees
        FROM users";
    
    $stats_stmt = $db->executeQuery($stats_query);
    $stats = $stats_stmt->fetch();
    
} catch (Exception $e) {
    Config::logError("Users page error: " . $e->getMessage());
    $users = [];
    $total_users = 0;
    $total_pages = 1;
    $stats = ['total' => 0, 'active' => 0, 'admins' => 0, 'supervisors' => 0, 'employees' => 0];
}

$page_title = "قائمة المستخدمين";
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">قائمة المستخدمين</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="add-user.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            إضافة مستخدم جديد
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>
                            طباعة
                        </button>
                    </div>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="h4 mb-0"><?php echo number_format($stats['total']); ?></div>
                                    <div>إجمالي المستخدمين</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="h4 mb-0"><?php echo number_format($stats['active']); ?></div>
                                    <div>المستخدمين النشطين</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="h4 mb-0"><?php echo number_format($stats['admins']); ?></div>
                                    <div>المديرين</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-shield fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="h4 mb-0"><?php echo number_format($stats['employees']); ?></div>
                                    <div>الموظفين</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-tie fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج البحث والتصفية -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="اسم المستخدم، الاسم، البريد...">
                        </div>
                        <div class="col-md-3">
                            <label for="role" class="form-label">الصلاحية</label>
                            <select class="form-select" id="role" name="role">
                                <option value="">جميع الصلاحيات</option>
                                <option value="admin" <?php echo $role == 'admin' ? 'selected' : ''; ?>>مدير</option>
                                <option value="supervisor" <?php echo $role == 'supervisor' ? 'selected' : ''; ?>>مشرف</option>
                                <option value="employee" <?php echo $role == 'employee' ? 'selected' : ''; ?>>موظف</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="active" <?php echo $status == 'active' ? 'selected' : ''; ?>>نشط</option>
                                <option value="inactive" <?php echo $status == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                                <a href="users.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>
                                    إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول المستخدمين -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        المستخدمين (<?php echo number_format($total_users); ?> مستخدم)
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0 sortable-table">
                            <thead class="table-dark">
                                <tr>
                                    <th data-sort="username">اسم المستخدم</th>
                                    <th data-sort="full_name">الاسم الكامل</th>
                                    <th data-sort="email">البريد الإلكتروني</th>
                                    <th data-sort="role">الصلاحية</th>
                                    <th>معلومات إضافية</th>
                                    <th data-sort="is_active">الحالة</th>
                                    <th data-sort="last_login">آخر دخول</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($users)): ?>
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">لا توجد مستخدمين مطابقة لمعايير البحث</p>
                                        <a href="add-user.php" class="btn btn-primary">إضافة مستخدم جديد</a>
                                    </td>
                                </tr>
                                <?php else: ?>
                                <?php foreach ($users as $user): ?>
                                <tr>
                                    <td data-sort="username">
                                        <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                        <?php if ($user['id'] == $user_id): ?>
                                        <span class="badge bg-info ms-1">أنت</span>
                                        <?php endif; ?>
                                    </td>
                                    <td data-sort="full_name">
                                        <?php echo htmlspecialchars($user['full_name']); ?>
                                    </td>
                                    <td data-sort="email">
                                        <?php echo htmlspecialchars($user['email']); ?>
                                    </td>
                                    <td data-sort="role">
                                        <?php
                                        $roles = [
                                            'admin' => '<span class="badge bg-danger">مدير</span>',
                                            'supervisor' => '<span class="badge bg-warning">مشرف</span>',
                                            'employee' => '<span class="badge bg-info">موظف</span>'
                                        ];
                                        echo $roles[$user['role']] ?? $user['role'];
                                        ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($user['phone'])): ?>
                                        <div><i class="fas fa-phone text-info me-1"></i><?php echo htmlspecialchars($user['phone']); ?></div>
                                        <?php endif; ?>
                                        <?php if (!empty($user['department'])): ?>
                                        <div><i class="fas fa-building text-secondary me-1"></i><?php echo htmlspecialchars($user['department']); ?></div>
                                        <?php endif; ?>
                                    </td>
                                    <td data-sort="is_active">
                                        <?php if ($user['is_active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">غير نشط</span>
                                        <?php endif; ?>
                                    </td>
                                    <td data-sort="last_login">
                                        <?php if ($user['last_login']): ?>
                                        <?php echo formatDateArabic($user['last_login']); ?>
                                        <?php else: ?>
                                        <span class="text-muted">لم يسجل دخول</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="edit-user.php?id=<?php echo $user['id']; ?>" 
                                               class="btn btn-outline-primary" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if ($user['id'] != $user_id): ?>
                                            <button type="button" class="btn btn-outline-warning" 
                                                    onclick="toggleUserStatus(<?php echo $user['id']; ?>)" 
                                                    title="<?php echo $user['is_active'] ? 'تعطيل' : 'تفعيل'; ?>">
                                                <i class="fas <?php echo $user['is_active'] ? 'fa-user-slash' : 'fa-user-check'; ?>"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="deleteUser(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')" 
                                                    title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- ترقيم الصفحات -->
                <?php if ($total_pages > 1): ?>
                <div class="card-footer">
                    <nav aria-label="ترقيم الصفحات">
                        <ul class="pagination justify-content-center mb-0">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">السابق</a>
                            </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                            </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">التالي</a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
</div>

<!-- نماذج التأكيد -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف المستخدم <strong id="deleteUsername"></strong>؟ 
                لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="user_id" id="deleteUserId">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="toggleStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد تغيير الحالة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من تغيير حالة هذا المستخدم؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="toggle_status">
                    <input type="hidden" name="user_id" id="toggleUserId">
                    <button type="submit" class="btn btn-warning">تغيير الحالة</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deleteUser(userId, username) {
    document.getElementById('deleteUserId').value = userId;
    document.getElementById('deleteUsername').textContent = username;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

function toggleUserStatus(userId) {
    document.getElementById('toggleUserId').value = userId;
    const modal = new bootstrap.Modal(document.getElementById('toggleStatusModal'));
    modal.show();
}
</script>

<?php include 'includes/footer.php'; ?>
